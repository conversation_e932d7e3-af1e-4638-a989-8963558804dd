// R2状态读取器 - 直接从R2存储读取状态文件
class R2StatusReader {
    constructor(taskId, userEmail) {
        this.taskId = taskId;
        this.userEmail = userEmail;
        this.retryCount = 0;
        this.maxRetries = 5;
        this.baseInterval = 120000; // 2分钟
        this.statusCheckInterval = null;
        this.isRunning = false;
        this.onStatusUpdateCallback = null;
        this.onErrorCallback = null;
    }

    // 生成邮箱哈希值 - 与后端保持一致
    hashEmail(email) {
        if (!email || typeof email !== 'string' || email.trim() === '') {
            throw new Error('邮箱地址不能为空或无效');
        }

        const cleanEmail = email.trim().toLowerCase();

        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(cleanEmail)) {
            throw new Error(`邮箱格式无效: ${email}`);
        }

        // 使用与R2StorageManager相同的哈希算法
        let hash = 0;
        for (let i = 0; i < cleanEmail.length; i++) {
            const char = cleanEmail.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }

        const hashResult = Math.abs(hash).toString(36);
        return hashResult;
    }

    // 生成状态文件路径
    generateStatusPath() {
        const emailHash = this.hashEmail(this.userEmail);
        return `users/${emailHash}/tasks/${this.taskId}/status.json`;
    }

    // 开始状态检查
    start() {
        if (this.isRunning) return;

        this.isRunning = true;
        this.retryCount = 0;
        this.checkStatus();
    }

    // 停止状态检查
    stop() {
        this.isRunning = false;
        if (this.statusCheckInterval) {
            clearTimeout(this.statusCheckInterval);
            this.statusCheckInterval = null;
        }
    }

    // 直接从R2存储读取状态文件
    async readStatusFromR2() {
        const statusPath = this.generateStatusPath();
        console.log(`📁 读取R2状态文件: ${statusPath}`);

        try {
            // 调用前端API来读取R2文件
            const response = await fetch('/api/r2-file-reader', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filePath: statusPath
                })
            });

            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('状态文件不存在');
                }
                throw new Error(`读取状态文件失败: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error || '读取状态文件失败');
            }

            return result.data;
        } catch (error) {
            console.error(`读取R2状态文件失败: ${error.message}`);
            throw error;
        }
    }

    // 检查任务状态
    async checkStatus() {
        if (!this.isRunning) return;

        try {
            console.log(`🔍 直接从R2读取任务状态: ${this.taskId}`);

            // 直接从R2存储读取状态文件
            const statusData = await this.readStatusFromR2();

            // 重置重试计数器
            this.retryCount = 0;

            // 处理状态更新 - 直接使用R2状态数据
            this.handleStatusUpdate(statusData);

            // 根据状态决定是否继续轮询
            if (this.shouldContinuePolling(statusData.status)) {
                this.scheduleNextCheck(this.baseInterval);
            } else {
                this.stop();
            }

        } catch (error) {
            this.handleError(error);
        }
    }

    // 处理速率限制
    handleRateLimit(response) {
        const retryAfter = response.headers.get('Retry-After');
        const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : 30000;

        console.warn(`速率限制触发，${waitTime/1000}秒后重试`);
        this.onStatusUpdate({
            type: 'rate_limit',
            message: `请求过于频繁，${waitTime/1000}秒后自动重试...`,
            waitTime: waitTime
        });

        this.scheduleNextCheck(waitTime);
    }

    // 处理错误
    handleError(error) {
        this.retryCount++;

        if (this.retryCount >= this.maxRetries) {
            this.stop();
            this.onError({
                type: 'max_retries_exceeded',
                message: `连续查询失败 ${this.maxRetries} 次: ${error.message}`,
                error: error
            });
            return;
        }

        // 指数退避重试
        const delay = this.baseInterval * Math.pow(2, this.retryCount - 1);
        console.warn(`状态查询失败 (${this.retryCount}/${this.maxRetries})，${delay/1000}秒后重试:`, error.message);

        this.onStatusUpdate({
            type: 'retry',
            message: `查询失败，${delay/1000}秒后重试 (${this.retryCount}/${this.maxRetries})`,
            error: error.message
        });

        this.scheduleNextCheck(delay);
    }

    // 处理状态更新
    handleStatusUpdate(status) {
        this.onStatusUpdate({
            type: 'status_update',
            status: status
        });
    }

    // 判断是否应该继续轮询
    shouldContinuePolling(status) {
        const finalStates = ['completed', 'failed'];
        return !finalStates.includes(status);
    }

    // 安排下次检查
    scheduleNextCheck(delay) {
        if (!this.isRunning) return;

        this.statusCheckInterval = setTimeout(() => {
            this.checkStatus();
        }, delay);
    }

    // 状态更新回调（需要外部实现）
    onStatusUpdate(update) {
        if (this.onStatusUpdateCallback) {
            this.onStatusUpdateCallback(update);
        } else {
            console.log('状态更新:', update);
        }
    }

    // 错误回调（需要外部实现）
    onError(error) {
        if (this.onErrorCallback) {
            this.onErrorCallback(error);
        } else {
            console.error('任务状态检查错误:', error);
        }
    }

    // 设置回调函数
    setOnStatusUpdate(callback) {
        this.onStatusUpdateCallback = callback;
    }

    setOnError(callback) {
        this.onErrorCallback = callback;
    }
}

// 应用状态管理
class AppState {
    constructor() {
        this.user = null;
        this.currentPage = 'home';
        this.currentBookId = null;
        this.currentLibrarySort = 'recent';
        this.enhancedPlayer = null; // 增强版播放器实例
        this.tasks = []; // 初始化任务数组
        this.allTasks = []; // 初始化所有任务数组
        this.taskStatusCheckers = new Map(); // 管理活动的状态检查器
        this.refreshTimeout = null; // 防抖刷新定时器
        this.init();
    }

    init() {
        this.loadUserFromStorage();
        this.loadThemeFromStorage();
        this.setupEventListeners();
        this.updateUI();

        // 如果用户已登录，自动获取最新的用户数据
        if (this.user) {
            this.syncUserDataFromServer();
        }

        // 添加页面可见性监听器 - 当用户重新回到页面时同步状态
        this.setupPageVisibilityListener();
    }

    // 新增：页面可见性监听器
    setupPageVisibilityListener() {
        // 当页面重新变为可见时，同步任务状态
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.user) {
                console.log('📱 页面重新可见，主动同步任务状态...');

                // 延迟一点执行，确保页面完全加载
                setTimeout(() => {
                    // 不仅在my-tasks页面，在任何页面都检查状态
                    if (this.allTasks && this.allTasks.length > 0) {
                        // 使用新的混合方案进行状态同步
                        this.hybridTaskStatusSync();
                    } else if (this.currentPage === 'my-tasks') {
                        // 如果还没有加载任务，先加载任务再检查
                        this.loadMyTasks();
                    }
                }, 500);
            }
        });

        // 添加焦点监听器（备用方案）
        window.addEventListener('focus', () => {
            if (this.user && this.currentPage === 'my-tasks') {
                console.log('🔍 窗口重新获得焦点，检查任务状态...');
                setTimeout(() => {
                    if (this.allTasks && this.allTasks.length > 0) {
                        this.syncTasksStatus();
                    }
                }, 300);
            }
        });
    }

    // 启动任务状态检查
    startTaskStatusCheck(taskId, apiBase = '') {
        // 如果已经有检查器在运行，先停止它
        if (this.taskStatusCheckers.has(taskId)) {
            this.stopTaskStatusCheck(taskId);
        }

        const statusChecker = new R2StatusReader(taskId, this.user?.email);
        
        // 设置回调函数
        statusChecker.setOnStatusUpdate((update) => {
            this.handleTaskStatusUpdate(taskId, update);
        });

        statusChecker.setOnError((error) => {
            this.handleTaskStatusError(taskId, error);
        });

        this.taskStatusCheckers.set(taskId, statusChecker);
        statusChecker.start();
        
        console.log(`已启动任务 ${taskId} 的状态检查`);
    }

    // 停止任务状态检查
    stopTaskStatusCheck(taskId) {
        const statusChecker = this.taskStatusCheckers.get(taskId);
        if (statusChecker) {
            statusChecker.stop();
            this.taskStatusCheckers.delete(taskId);
            console.log(`已停止任务 ${taskId} 的状态检查`);
        }
    }

    // 停止所有任务状态检查
    stopAllTaskStatusChecks() {
        for (const [taskId, statusChecker] of this.taskStatusCheckers) {
            statusChecker.stop();
        }
        this.taskStatusCheckers.clear();
        console.log('已停止所有任务状态检查');
    }

    // 处理任务状态更新
    handleTaskStatusUpdate(taskId, update) {
        switch(update.type) {
            case 'status_update':
                console.log(`任务 ${taskId} 状态更新:`, update.status);
                // 更新本地任务状态
                this.updateLocalTaskStatus(taskId, update.status);
                
                // 优先使用精确的局部更新（只更新最新任务的进度条）
                const localUpdateSuccess = this.updateTaskElementDirectly(taskId, update.status);
                
                if (!localUpdateSuccess) {
                    // 如果不是最新任务或局部更新失败，不进行任何页面刷新
                    // 这避免了其他任务引起的页面闪烁
                    console.log(`任务 ${taskId} 不是最新任务，跳过UI更新`);
                }
                break;
                
            case 'rate_limit':
                console.warn(`任务 ${taskId} 速率限制:`, update.message);
                this.showMessage(update.message, 'warning');
                break;
                
            case 'retry':
                console.info(`任务 ${taskId} 重试:`, update.message);
                // 可以选择显示重试信息，但不要过于频繁
                break;
        }
    }

    // 处理任务状态检查错误
    handleTaskStatusError(taskId, error) {
        console.error(`任务 ${taskId} 状态检查失败:`, error);
        // 对于网络错误等，显示用户友好的消息
        if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
            // 网络错误不显示给用户，避免过多通知
            return;
        }
        this.showMessage(`任务 ${taskId} 状态检查失败: ${error.message}`, 'error');
    }

    // 更新本地任务状态
    updateLocalTaskStatus(taskId, statusData) {
        // 更新tasks数组
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            this.tasks[taskIndex] = { ...this.tasks[taskIndex], ...statusData };
        }

        // 更新allTasks数组
        const allTaskIndex = this.allTasks.findIndex(task => task.id === taskId);
        if (allTaskIndex !== -1) {
            this.allTasks[allTaskIndex] = { ...this.allTasks[allTaskIndex], ...statusData };
        }
    }

    // 新版：精确局部更新 - 只更新最新任务的进度条和状态，避免页面闪烁
    updateTaskElementDirectly(taskId, statusData) {
        // 只在"我的任务"页面进行精确更新
        if (this.currentPage !== 'my-tasks') {
            return false;
        }

        // 检查是否是最新的任务（第一个任务元素）
        const allTaskElements = document.querySelectorAll('.my-task-item[data-task-id]');
        if (allTaskElements.length === 0) return false;
        
        const latestTaskElement = allTaskElements[0]; // 第一个就是最新的
        const latestTaskId = latestTaskElement.getAttribute('data-task-id');
        
        // 只更新最新的任务
        if (taskId !== latestTaskId) {
            console.log(`跳过任务 ${taskId} 的更新，只更新最新任务 ${latestTaskId}`);
            return false;
        }

        try {
            // 1. 精确更新状态徽章（无闪烁）
            const statusBadge = latestTaskElement.querySelector('.task-status-badge');
            if (statusBadge) {
                const newStatus = statusData.status.toLowerCase();
                const newText = this.getStatusText(statusData.status);
                
                // 只在状态真正改变时才更新
                if (!statusBadge.classList.contains(newStatus) || statusBadge.textContent !== newText) {
                    statusBadge.className = `task-status-badge ${newStatus}`;
                    statusBadge.textContent = newText;
                }
            }

            // 2. 精确更新进度条（核心功能）
            if (statusData.progress !== undefined && statusData.status === 'pending') {
                this.updateProgressBarOnly(latestTaskElement, statusData.progress);
            } else if (statusData.status !== 'pending') {
                // 任务完成后移除进度条
                this.removeProgressBarOnly(latestTaskElement);
            }

            // 3. 更新任务项样式类（无闪烁）
            const newStatusClass = `status-${statusData.status.toLowerCase()}`;
            if (!latestTaskElement.classList.contains(newStatusClass)) {
                // 移除旧的状态类
                latestTaskElement.classList.remove('status-pending', 'status-completed', 'status-failed');
                latestTaskElement.classList.add(newStatusClass);
            }

            // 4. 任务完成时的特殊处理
            if (this.isTaskCompleted(statusData.status)) {
                this.handleTaskCompletion(latestTaskElement, taskId);
            }

            console.log(`✅ 任务 ${taskId} 进度条已精确更新: ${statusData.progress}%`);
            return true;
        } catch (error) {
            console.warn(`任务 ${taskId} 精确更新失败:`, error);
            return false;
        }
    }

    // 新增：只更新进度条，避免任何DOM重建
    updateProgressBarOnly(taskElement, progress) {
        let progressContainer = taskElement.querySelector('.task-progress');
        
        if (!progressContainer) {
            // 如果没有进度条容器，动态创建（只创建一次）
            progressContainer = document.createElement('div');
            progressContainer.className = 'task-progress';
            progressContainer.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%; transition: width 0.3s ease;"></div>
                </div>
                <p class="progress-text" style="text-align: center; margin-top: 0.5rem; color: #666; font-size: 0.9rem;">
                    处理进度: 0%
                </p>
            `;
            
            // 插入到任务头部后面
            const header = taskElement.querySelector('.task-header');
            if (header) {
                header.insertAdjacentElement('afterend', progressContainer);
            }
        }
        
        // 精确更新进度条宽度和文字（使用缓存的元素引用）
        const progressFill = progressContainer.querySelector('.progress-fill');
        const progressText = progressContainer.querySelector('.progress-text');
        
        if (progressFill && progressText) {
            // 只在进度真正变化时才更新
            const currentWidth = progressFill.style.width;
            const newWidth = `${progress}%`;
            const newText = `处理进度: ${progress}%`;
            
            if (currentWidth !== newWidth) {
                progressFill.style.width = newWidth;
            }
            
            if (progressText.textContent !== newText) {
                progressText.textContent = newText;
            }
        }
    }

    // 新增：只移除进度条，避免重新渲染
    removeProgressBarOnly(taskElement) {
        const progressContainer = taskElement.querySelector('.task-progress');
        if (progressContainer) {
            // 平滑移除进度条
            progressContainer.style.opacity = '0';
            progressContainer.style.transition = 'opacity 0.3s ease';
            
            setTimeout(() => {
                if (progressContainer.parentNode) {
                    progressContainer.remove();
                }
            }, 300);
        }
    }

    // 新增：处理任务完成的特殊效果
    handleTaskCompletion(taskElement, taskId) {
        // 任务完成动画（轻微的，不影响布局）
        taskElement.style.transform = 'scale(1.01)';
        taskElement.style.transition = 'transform 0.3s ease';
        
        setTimeout(() => {
            taskElement.style.transform = 'scale(1)';
            
            // 停止该任务的状态检查
            this.stopTaskStatusCheck(taskId);
            
            // 如果所有任务都完成了，显示提示
            setTimeout(() => {
                if (!this.hasProcessingTasks()) {
                    this.showMessage('✅ 所有任务已完成！', 'success');
                }
            }, 500);
        }, 300);
    }

    // 防抖的页面刷新
    debouncedRefreshTaskDisplays() {
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout);
        }
        this.refreshTimeout = setTimeout(() => {
            this.refreshTaskDisplays();
        }, 500); // 500ms防抖延迟
    }

    // 刷新任务显示
    refreshTaskDisplays() {
        if (this.currentPage === 'workshop') {
            this.renderTasks();
        } else if (this.currentPage === 'my-tasks') {
            this.renderMyTasks();
        }
    }

    // 新增：检查任务是否已完成
    isTaskCompleted(status) {
        const completedStatuses = ['completed', 'failed'];
        return completedStatuses.includes(status);
    }

    // 新增：检查是否有正在处理的任务
    hasProcessingTasks() {
        const allTasks = [...this.tasks, ...this.allTasks];
        return allTasks.some(task =>
            task.status === 'pending'
        );
    }

    // 智能启动状态检查 - 优先监控最新的处理中任务
    startProcessingTasksCheck() {
        if (!this.user) return;

        // 检查紧急停止信号
        const emergencyStop = localStorage.getItem('EMERGENCY_STOP_POLLING');
        if (emergencyStop === 'true') {
            console.log('⚠️ 检测到紧急停止信号，跳过状态检查');
            return;
        }

        // 合并所有任务并去重
        const allTasks = [...this.tasks, ...this.allTasks]
            .filter((task, index, self) => self.findIndex(t => t.id === task.id) === index);

        // 按创建时间排序，获取最新的处理中任务
        const processingTasks = allTasks
            .filter(task => task.status === 'pending')
            .sort((a, b) => new Date(b.created_at || b.createdAt) - new Date(a.created_at || a.createdAt));

        if (processingTasks.length === 0) {
            console.log('没有正在处理的任务，停止所有状态检查');
            this.stopAllTaskStatusChecks();
            return;
        }

        // 优先监控最新的任务（在"我的任务"页面只监控最新任务）
        if (this.currentPage === 'my-tasks') {
            const latestTask = processingTasks[0];
            console.log(`在"我的任务"页面，只监控最新任务: ${latestTask.id}`);
            
            // 停止其他任务的监控
            for (const [taskId, checker] of this.taskStatusCheckers) {
                if (taskId !== latestTask.id) {
                    console.log(`停止非最新任务 ${taskId} 的状态检查`);
                    this.stopTaskStatusCheck(taskId);
                }
            }
            
            // 确保最新任务有状态检查
            if (!this.taskStatusCheckers.has(latestTask.id)) {
                console.log(`为最新任务 ${latestTask.id} 启动状态检查`);
                this.startTaskStatusCheck(latestTask.id, '');
            }
        } else {
            // 在其他页面，监控所有处理中的任务
            console.log(`在${this.currentPage}页面，监控 ${processingTasks.length} 个处理中任务`);
            
            for (const task of processingTasks) {
                if (!this.taskStatusCheckers.has(task.id)) {
                    console.log(`为任务 ${task.id} 启动状态检查`);
                    this.startTaskStatusCheck(task.id, '');
                }
            }

            // 停止不需要监控的任务
            for (const [taskId, checker] of this.taskStatusCheckers) {
                const isTaskStillProcessing = processingTasks.some(task => task.id === taskId);
                if (!isTaskStillProcessing) {
                    console.log(`任务 ${taskId} 已不在处理中，停止状态检查`);
                    this.stopTaskStatusCheck(taskId);
                }
            }
        }
    }

    // 主题管理
    loadThemeFromStorage() {
        const savedTheme = localStorage.getItem('audiobook_theme') || 'light';
        this.setTheme(savedTheme);
    }

    setTheme(theme) {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('audiobook_theme', theme);
        
        // 更新主题切换按钮图标
        const themeIcon = document.getElementById('theme-icon');
        if (themeIcon) {
            themeIcon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
        this.showMessage(`已切换到${newTheme === 'light' ? '浅色' : '深色'}主题`, 'info');
    }

    // 用户数据管理
    loadUserFromStorage() {
        const userData = localStorage.getItem('audiobook_user');
        if (userData) {
            this.user = JSON.parse(userData);
        }
    }

    saveUserToStorage() {
        if (this.user) {
            localStorage.setItem('audiobook_user', JSON.stringify(this.user));
        } else {
            localStorage.removeItem('audiobook_user');
        }
    }

    // 从服务器同步用户数据
    async syncUserDataFromServer() {
        if (!this.user) return;

        try {
            const response = await fetch('/api/auth/me', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });

            if (response.ok) {
                const userData = await response.json();
                // 修复：后端直接返回用户数据，不需要 userData.user 包装
                if (userData && userData.points !== undefined) {
                    // 检查积分是否有变化
                    const oldPoints = this.user.points;
                    const newPoints = userData.points;

                    // 只更新积分数据，保留用户的其他本地数据
                    this.user.points = newPoints;
                    // 注意：这里不会影响localStorage中的其他数据：
                    // - audiobook_playback_progress (音频播放位置)
                    // - audiobook_bookmarks (书签)
                    // - audiobook_theme (主题设置)
                    this.saveUserToStorage();

                    // 更新界面显示
                    const userPointsMini = document.getElementById('user-points-mini');
                    if (userPointsMini) {
                        userPointsMini.textContent = newPoints;
                    }

                    // 如果积分有变化，显示通知（仅在非初始化时）
                    if (oldPoints !== newPoints && oldPoints !== undefined) {
                        const change = newPoints - oldPoints;
                        if (change > 0) {
                            this.showMessage(`积分增加 +${change}，当前积分：${newPoints}`, 'success');
                        } else if (change < 0) {
                            this.showMessage(`积分减少 ${change}，当前积分：${newPoints}`, 'info');
                        }
                    }

                    console.log('用户积分已同步:', newPoints);
                } else {
                    console.warn('服务器返回的用户数据格式异常，数据:', userData);
                }
            } else {
                console.warn('无法获取最新用户数据，响应状态:', response.status);
                // 如果是401未授权，可能需要重新登录
                if (response.status === 401) {
                    this.showMessage('登录已过期，请重新登录', 'error');
                }
            }
        } catch (error) {
            console.error('同步用户数据失败:', error);
            // 网络错误等情况下的处理
        }
    }

    // 事件监听器设置
    setupEventListeners() {
        // 主题切换
        document.getElementById('theme-toggle').addEventListener('click', () => this.toggleTheme());

        // 登录/注册相关
        document.getElementById('btn-login').addEventListener('click', () => this.showLoginModal());
        document.getElementById('btn-register').addEventListener('click', () => this.showRegisterModal());
        document.getElementById('btn-logout').addEventListener('click', () => this.handleLogout());
        document.getElementById('btn-checkin').addEventListener('click', () => this.handleCheckin());
        document.getElementById('btn-refresh-points').addEventListener('click', () => this.handleRefreshPoints());

        // 模态框关闭
        document.querySelector('.close').addEventListener('click', () => {
            document.getElementById('auth-modal').style.display = 'none';
        });

        // 表单切换
        document.getElementById('switch-to-register').addEventListener('click', (e) => {
            e.preventDefault();
            this.showRegisterModal();
        });

        document.getElementById('switch-to-login').addEventListener('click', (e) => {
            e.preventDefault();
            this.showLoginModal();
        });

        // 表单提交
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('registerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // 新首页按钮事件
        document.getElementById('btn-start-conversion').addEventListener('click', () => this.handleStartConversion());
        document.getElementById('btn-cta-register').addEventListener('click', () => this.showRegisterModal());
        document.getElementById('btn-cta-demo').addEventListener('click', () => this.handleDemoClick());

        // 导航菜单
        document.getElementById('nav-home').addEventListener('click', () => this.switchPage('home'));
        document.getElementById('nav-workshop').addEventListener('click', () => this.switchPage('workshop'));
        document.getElementById('nav-tasks').addEventListener('click', () => this.switchPage('my-tasks'));
        document.getElementById('nav-library').addEventListener('click', () => this.switchPage('my-library'));
        
        // 积分点击事件 - 使用事件委托
        document.addEventListener('click', (e) => {
            if (e.target.id === 'points-clickable' || e.target.closest('#points-clickable')) {
                this.switchPage('my-points');
            }
        });

        // 上传相关 - 添加防护性检查
        const tabFile = document.getElementById('tab-file');
        if (tabFile) tabFile.addEventListener('click', () => this.switchUploadTab('file'));

        const tabUrl = document.getElementById('tab-url');
        if (tabUrl) tabUrl.addEventListener('click', () => this.switchUploadTab('url'));

        // 简化的上传按钮处理
        const uploadBtn = document.getElementById('upload-btn');
        if (uploadBtn) uploadBtn.addEventListener('click', () => {
            const fileInput = document.getElementById('file-input');
            if (fileInput) fileInput.click();
        });

        const fileInput = document.getElementById('file-input');
        if (fileInput) fileInput.addEventListener('change', (e) => {
            if (e.target.files[0]) {
                this.handleFileUpload(e.target.files[0]);
            }
        });

        const btnUrlSubmit = document.getElementById('btn-url-submit');
        if (btnUrlSubmit) btnUrlSubmit.addEventListener('click', () => this.handleUrlSubmit());

        // 创作工坊上传功能 - 添加防护性检查
        const workshopTabFile = document.getElementById('workshop-tab-file');
        if (workshopTabFile) workshopTabFile.addEventListener('click', () => this.switchWorkshopUploadTab('file'));

        const workshopTabUrl = document.getElementById('workshop-tab-url');
        if (workshopTabUrl) workshopTabUrl.addEventListener('click', () => this.switchWorkshopUploadTab('url'));

        const workshopUploadZone = document.getElementById('workshop-upload-zone');
        if (workshopUploadZone) workshopUploadZone.addEventListener('click', () => {
            const workshopFileInput = document.getElementById('workshop-file-input');
            if (workshopFileInput) workshopFileInput.click();
        });

        const workshopFileInput = document.getElementById('workshop-file-input');
        if (workshopFileInput) workshopFileInput.addEventListener('change', (e) => {
            if (e.target.files[0]) {
                this.handleFileUpload(e.target.files[0]);
            }
        });

        const workshopBtnUrlSubmit = document.getElementById('workshop-btn-url-submit');
        if (workshopBtnUrlSubmit) workshopBtnUrlSubmit.addEventListener('click', () => this.handleWorkshopUrlSubmit());

        // 任务过滤器
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.filterTasks(e.target.dataset.status);
            });
        });

        // 任务状态刷新按钮 - 添加防护性检查
        const refreshTasksBtn = document.getElementById('refresh-tasks-btn');
        if (refreshTasksBtn) refreshTasksBtn.addEventListener('click', () => this.handleRefreshTasks());

        // 音频库过滤器
        document.querySelectorAll('.library-filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.filterLibrary(e.target.dataset.sort);
            });
        });

        // 音频播放器控制
        if (document.getElementById('back-to-library')) {
            document.getElementById('back-to-library').addEventListener('click', () => this.switchPage('my-library'));
        }

        // 播放器控制事件将在音频播放器页面显示时动态绑定

        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('auth-modal');
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        // 处理选项相关事件 - 添加防护性检查
        const processingEnhanced = document.getElementById('processing-enhanced');
        if (processingEnhanced) processingEnhanced.addEventListener('change', (e) => {
            this.toggleEnhancedOptions(e.target.checked);
        });

        const urlProcessingEnhanced = document.getElementById('url-processing-enhanced');
        if (urlProcessingEnhanced) urlProcessingEnhanced.addEventListener('change', (e) => {
            this.updateCostInfo(e.target.checked);
        });

        // 增强版播放器相关事件 - 添加防护性检查
        const backToLibraryEnhanced = document.getElementById('back-to-library-enhanced');
        if (backToLibraryEnhanced) backToLibraryEnhanced.addEventListener('click', () => {
            this.switchPage('my-library');
        });
    }

    // 页面切换
    switchPage(page) {
        // 页面ID映射 - 将逻辑页面名称映射到实际的HTML元素ID
        const pageIdMap = {
            'home': 'home-content',
            'workshop': 'workshop-content', 
            'my-tasks': 'my-tasks-content',
            'my-library': 'my-library-content',
            'my-points': 'my-points-content',
            'audio-player-page': 'audio-player-page',
            'enhanced-audio-player-page': 'enhanced-audio-player-page'
        };

        // 隐藏所有页面
        document.querySelectorAll('.page-content').forEach(p => p.style.display = 'none');
        
        // 清理播放器实例
        if (page !== 'audio-player-page' && page !== 'enhanced-audio-player-page') {
            if (this.enhancedPlayer) {
                this.enhancedPlayer.destroy();
                this.enhancedPlayer = null;
            }
        }
        
        // 获取实际的页面ID
        const actualPageId = pageIdMap[page] || page;
        
        // 显示目标页面
        const targetPage = document.getElementById(actualPageId);
        if (targetPage) {
            targetPage.style.display = 'block';
            console.log(`成功切换到页面: ${page} (ID: ${actualPageId})`);
        } else {
            console.error(`找不到页面元素: ${page} (ID: ${actualPageId})`);
        }

        // 更新导航状态
        document.querySelectorAll('.nav-btn').forEach(btn => btn.classList.remove('active'));
        const activeBtn = document.querySelector(`[data-page="${page}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // 页面特定初始化
        switch (page) {
            case 'home':
                this.loadTasks().then(() => {
                    // 加载完任务后启动状态检查
                    setTimeout(() => this.startProcessingTasksCheck(), 1000);
                });
                break;
            case 'workshop':
                this.loadTasks().then(() => {
                    // 创作工坊页面也需要加载任务数据来显示最近任务
                    setTimeout(() => this.startProcessingTasksCheck(), 1000);
                });
                break;
            case 'my-tasks':
                this.loadMyTasks().then(() => {
                    // 加载完我的任务后启动状态检查
                    setTimeout(() => this.startProcessingTasksCheck(), 1000);
                });
                break;
            case 'my-library':
                this.loadAudioLibrary();
                // 停止任务状态检查，因为在音频库页面不需要
                this.stopAllTaskStatusChecks();
                break;
            case 'my-points':
                this.loadPointsHistory();
                // 停止任务状态检查，因为在积分页面不需要
                this.stopAllTaskStatusChecks();
                break;
            case 'enhanced-audio-player-page':
                // 增强版播放器页面已在 initEnhancedPlayer 中初始化
                // 停止任务状态检查，因为在播放器页面不需要
                this.stopAllTaskStatusChecks();
                break;
            default:
                // 对于其他页面，停止所有状态检查
                this.stopAllTaskStatusChecks();
                break;
        }

        this.currentPage = page;
    }

    // UI更新
    updateUI() {
        const navMenu = document.getElementById('nav-menu');
        const navUser = document.getElementById('nav-user');
        const navUserInfo = document.getElementById('nav-user-info');
        const landingSection = document.getElementById('landing-section');

        if (this.user) {
            // 登录后的UI更新
            navMenu.style.display = 'flex';
            navUser.style.display = 'none';
            navUserInfo.style.display = 'flex';
            
            // 更新用户信息
            document.getElementById('user-email-mini').textContent = this.user.email;
            document.getElementById('user-points-mini').textContent = this.user.points || 100;
            
            // 更新每日签到按钮状态
            this.updateCheckinButton();
            
            // 如果在首页，显示landing页面（登录用户也看到相同的介绍页面）
            if (this.currentPage === 'home') {
                landingSection.style.display = 'block';
            }
        } else {
            // 未登录的UI更新
            navMenu.style.display = 'none';
            navUser.style.display = 'flex';
            navUserInfo.style.display = 'none';
            
            // 如果在首页，显示landing页面
            if (this.currentPage === 'home') {
                landingSection.style.display = 'block';
            }
        }
    }

    // 模态框显示
    showLoginModal() {
        document.getElementById('auth-modal').style.display = 'flex';
        document.getElementById('login-form').style.display = 'block';
        document.getElementById('register-form').style.display = 'none';
    }

    showRegisterModal() {
        document.getElementById('auth-modal').style.display = 'flex';
        document.getElementById('login-form').style.display = 'none';
        document.getElementById('register-form').style.display = 'block';
    }

    // 用户认证
    async handleLogin() {
        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;

        if (!email || !password) {
            this.showMessage('请输入邮箱和密码', 'error');
            return;
        }

        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, password })
            });

            const result = await response.json();

            if (response.ok) {
                // 保存token和用户信息
                localStorage.setItem('access_token', result.access_token);
                this.user = result.user;
                this.saveUserToStorage();
                this.updateUI();
                document.getElementById('auth-modal').style.display = 'none';
                this.showMessage('登录成功！', 'success');
                
                // 登录后启动状态检查
                setTimeout(() => {
                    this.loadTasks().then(() => {
                        this.startProcessingTasksCheck();
                    });
                }, 1000);
            } else {
                this.showMessage(result.error || '登录失败', 'error');
            }
        } catch (error) {
            console.error('登录失败:', error);
            this.showMessage('登录失败，请重试', 'error');
        }
    }

    async handleRegister() {
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;
        const confirmPassword = document.getElementById('register-confirm').value;

        if (password !== confirmPassword) {
            this.showMessage('两次输入的密码不一致', 'error');
            return;
        }

        try {
            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });

            const result = await response.json();

            if (response.ok) {
                // 保存token和用户信息
                localStorage.setItem('access_token', result.access_token);
                this.user = result.user;
                this.saveUserToStorage();
                this.updateUI();
                document.getElementById('auth-modal').style.display = 'none';
                this.showMessage('注册成功，获得100积分', 'success');
            } else {
                this.showMessage(result.error || '注册失败', 'error');
            }
        } catch (error) {
            this.showMessage('网络错误，请稍后重试', 'error');
        }
    }

    handleLogout() {
        this.user = null;
        this.tasks = [];
        this.currentPage = 'home';
        localStorage.removeItem('access_token');
        this.saveUserToStorage();
        this.updateUI();
        this.showMessage('已退出登录', 'info');
    }

    // 签到功能
    async handleCheckin() {
        try {
            const response = await fetch('/api/auth/checkin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });

            const result = await response.json();

            if (response.ok) {
                // 修复：后端返回的是 total_points 而不是 points，points_earned 而不是 reward
                this.user.points = result.total_points;
                this.user.lastCheckin = new Date().toISOString();
                this.saveUserToStorage();
                this.updateUI();
                this.showMessage(`签到成功！获得 ${result.points_earned} 积分`, 'success');
            } else {
                this.showMessage(result.error || '签到失败', 'error');
            }
        } catch (error) {
            this.showMessage('网络错误，请稍后重试', 'error');
        }
    }

    updateCheckinButton() {
        const checkinBtn = document.getElementById('btn-checkin');
        const now = new Date();
        const today = now.toDateString();
        
        if (this.user.lastCheckin) {
            const lastCheckinDate = new Date(this.user.lastCheckin).toDateString();
            if (lastCheckinDate === today) {
                checkinBtn.textContent = '今日已签到';
                checkinBtn.disabled = true;
                checkinBtn.classList.remove('btn-success');
                checkinBtn.classList.add('btn-outline');
            } else {
                checkinBtn.innerHTML = '<i class="fas fa-calendar-check"></i> 每日签到';
                checkinBtn.disabled = false;
                checkinBtn.classList.add('btn-success');
                checkinBtn.classList.remove('btn-outline');
            }
        }
    }

    // 上传功能
    switchUploadTab(type) {
        const fileTab = document.getElementById('tab-file');
        const urlTab = document.getElementById('tab-url');
        const fileUpload = document.getElementById('file-upload');
        const urlUpload = document.getElementById('url-upload');

        if (type === 'file') {
            fileTab.classList.add('active');
            urlTab.classList.remove('active');
            fileUpload.style.display = 'block';
            urlUpload.style.display = 'none';
        } else {
            fileTab.classList.remove('active');
            urlTab.classList.add('active');
            fileUpload.style.display = 'none';
            urlUpload.style.display = 'block';
        }
    }

    // 创作工坊的上传标签切换
    switchWorkshopUploadTab(type) {
        const fileTab = document.getElementById('workshop-tab-file');
        const urlTab = document.getElementById('workshop-tab-url');
        const fileUpload = document.getElementById('workshop-file-upload');
        const urlUpload = document.getElementById('workshop-url-upload');

        if (type === 'file') {
            fileTab.classList.add('active');
            urlTab.classList.remove('active');
            fileUpload.style.display = 'block';
            urlUpload.style.display = 'none';
        } else {
            fileTab.classList.remove('active');
            urlTab.classList.add('active');
            fileUpload.style.display = 'none';
            urlUpload.style.display = 'block';
        }
    }

    async handleFileUpload(file) {
        if (!this.validateFile(file)) {
            return;
        }

        if (this.user.points < 50) {
            this.showMessage('积分不足，需要50积分', 'error');
            return;
        }

        try {
            this.showMessage('正在上传文件...', 'info');
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('email', this.user.email);

            // 优先使用新的R2架构API
            let response;
            try {
                response = await fetch('/api/upload/file-r2', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    },
                    body: formData
                });

                // 如果R2 API失败，回退到传统API
                if (!response.ok && response.status === 404) {
                    console.log('R2 API不可用，回退到传统API');
                    response = await fetch('/api/upload/file', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                        },
                        body: formData
                    });
                }
            } catch (error) {
                console.log('R2 API调用失败，回退到传统API:', error);
                response = await fetch('/api/upload/file', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    },
                    body: formData
                });
            }

            const result = await response.json();

            if (response.ok) {
                // 显示成功消息，包含架构信息
                const architecture = result.architecture || 'KV';
                this.showMessage(`文件上传成功，开始转换... (${architecture}架构)`, 'success');

                // 刷新任务列表
                this.loadTasks();
                if (this.currentPage === 'tasks') {
                    this.loadMyTasks();
                }

                console.log(`✅ 文件上传成功，使用${architecture}架构，任务ID: ${result.taskId}`);
            } else {
                this.showMessage(result.error || '上传失败', 'error');
            }
        } catch (error) {
            this.showMessage('上传失败，请稍后重试', 'error');
        }
    }

    async handleUrlSubmit() {
        const urlInput = document.getElementById('url-input');
        const url = urlInput.value.trim();
        
        if (!url) {
            this.showMessage('请输入网页链接', 'error');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showMessage('请输入有效的网页链接', 'error');
            return;
        }

        if (!this.user) {
            this.showMessage('请先登录', 'error');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('url', url);
            
            const response = await fetch('/api/tasks/url', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.user.token}`
                },
                body: formData
            });

            if (response.ok) {
            const result = await response.json();
                this.showMessage('网页链接提交成功！开始转换...', 'success');
                urlInput.value = '';
                
                // 更新用户积分
                await this.updateUserPoints();
                
                // 刷新任务列表
                if (this.currentPage === 'tasks') {
                    this.loadMyTasks();
                } else {
                    this.loadTasks();
                }
            } else {
                const error = await response.json();
                this.showMessage(error.message || '提交失败，请重试', 'error');
            }
        } catch (error) {
            console.error('URL提交错误：', error);
            this.showMessage('网络错误，请检查连接', 'error');
        }
    }

    // 创作工坊的URL提交方法
    async handleWorkshopUrlSubmit() {
        const urlInput = document.getElementById('workshop-url-input');
        const url = urlInput.value.trim();

        if (!url) {
            this.showMessage('请输入网页链接', 'error');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showMessage('请输入有效的网页链接', 'error');
            return;
        }

        if (!this.user) {
            this.showMessage('请先登录', 'error');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('url', url);

            const response = await fetch('/api/tasks/url', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.user.token}`
                },
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                this.showMessage('网页链接提交成功！开始转换...', 'success');
                urlInput.value = '';
                
                // 更新用户积分
                await this.updateUserPoints();
                
                // 刷新任务列表
                this.loadTasks();
            } else {
                const error = await response.json();
                this.showMessage(error.message || '提交失败，请重试', 'error');
            }
        } catch (error) {
            console.error('URL提交错误：', error);
            this.showMessage('网络错误，请检查连接', 'error');
        }
    }

    // 验证函数
    validateFile(file) {
        const allowedTypes = [
            'text/plain',                                                           // TXT
            'application/epub+zip',                                                // EPUB
            'application/pdf',                                                     // PDF
            'application/msword',                                                  // DOC
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'  // DOCX
        ];
        const maxSize = 50 * 1024 * 1024; // 50MB

        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(txt|epub|pdf|doc|docx)$/i)) {
            this.showMessage('支持的格式：TXT、EPUB、PDF、DOC、DOCX', 'error');
            return false;
        }

        if (file.size > maxSize) {
            this.showMessage('文件大小不能超过 50MB', 'error');
            return false;
        }

        return true;
    }

    isValidUrl(string) {
        try {
            const url = new URL(string);
            return url.protocol === 'http:' || url.protocol === 'https:';
        } catch (_) {
            return false;
        }
    }

    // 任务管理 - 首页简单版本
    async loadTasks() {
        if (!this.user) return Promise.resolve();

        try {
            const response = await fetch(`/api/tasks`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            const result = await response.json();

            if (response.ok) {
                this.tasks = (result || []).map(task => this.formatTaskData(task));
                this.renderTasks();
                // 更新用户积分（防止积分状态不同步）
                this.updateUserPoints();
            }
        } catch (error) {
            console.error('加载任务失败:', error);
        }
    }

    // 更新用户积分
    async updateUserPoints() {
        if (!this.user) return;

        try {
            const response = await fetch('/api/auth/me', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });

            if (response.ok) {
                const userData = await response.json();
                // 修复：后端直接返回用户数据，不需要 userData.user 包装
                if (userData && userData.points !== undefined) {
                    this.user.points = userData.points;
                    this.saveUserToStorage();

                    // Update both mini display in navbar and any other points displays
                    const userPointsMini = document.getElementById('user-points-mini');
                    if (userPointsMini) {
                        userPointsMini.textContent = userData.points;
                    }

                    console.log('用户积分已更新:', userData.points);
                }
            }
        } catch (error) {
            console.error('Failed to update user points:', error);
        }
    }

    renderTasks() {
        const tasksContainer = document.getElementById('workshop-tasks-list');
        
        if (!tasksContainer) {
            console.error('找不到任务容器元素: workshop-tasks-list');
            return;
        }
        
        console.log('渲染任务列表，任务数量:', this.tasks ? this.tasks.length : 0);
        
        if (!this.tasks || this.tasks.length === 0) {
            tasksContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-tasks"></i>
                    <h3>暂无任务</h3>
                    <p>上传您的第一个图书文件开始转换吧！</p>
                </div>
            `;
            return;
        }

        // 只显示最近的 5 个任务
        const recentTasks = this.tasks.slice(0, 5);
        console.log('显示最近任务数量:', recentTasks.length);
        
        tasksContainer.innerHTML = recentTasks.map(task => {
            // 使用通用日期格式化方法
            const formattedDate = this.formatDate(task.created_at || task.createdAt, '未知时间');

            return `
                <div class="task-item" data-task-id="${task.id}">
                    <div class="task-info">
                        <h4>${task.filename || task.title || '未知文件'}</h4>
                        <p>
                            <i class="fas fa-clock"></i>
                            ${formattedDate}
                        </p>
                    </div>
                    <div class="task-status ${this.getStatusClass(task.status)}">
                        <i class="${this.getStatusIcon(task.status)}"></i>
                        ${this.getStatusText(task.status)}
                    </div>
                </div>
            `;
        }).join('');
        
        console.log('任务列表渲染完成');
    }

    // 我的任务页面 - 完整版本（修复API调用）
    async loadMyTasks() {
        if (!this.user) return;

        const myTasksList = document.getElementById('my-tasks-list');
        myTasksList.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在加载任务列表...</div>';

        try {
            // 使用现有的 /api/tasks 端点并添加认证令牌
            const response = await fetch(`/api/tasks`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });

            const result = await response.json();

            if (response.ok) {
                // 后端返回的是任务数组，前端期望的是 {tasks: [...]} 格式
                this.allTasks = (result || []).map(task => this.formatTaskData(task));

                console.log(`📋 加载了 ${this.allTasks.length} 个任务`);

                this.renderMyTasks();

                // 启动状态同步机制
                this.startR2StatusSync();
            } else {
                myTasksList.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>' + (result.detail || result.error || '请刷新页面重试') + '</p></div>';
            }
        } catch (error) {
            console.error('加载我的任务失败:', error);
            myTasksList.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>请检查网络连接</p></div>';
        }
    }

    // 新增：R2状态同步机制
    startR2StatusSync() {
        // 延迟执行状态同步，避免影响页面渲染
        setTimeout(async () => {
            try {
                // 检查所有非最终状态的任务
                const pendingTasks = this.allTasks.filter(task => task.status === 'pending');

                if (pendingTasks.length > 0) {
                    console.log(`🔍 发现 ${pendingTasks.length} 个待处理任务，启动状态轮询`);

                    // 首先进行一次状态同步检查
                    await this.performR2StatusSync(pendingTasks);

                    // 为仍然是pending状态的任务启动定期轮询
                    const stillPendingTasks = this.allTasks.filter(task => task.status === 'pending');
                    for (const task of stillPendingTasks) {
                        this.startTaskStatusCheck(task.id, '');
                    }
                } else {
                    console.log('📊 所有任务都已完成，无需状态同步');
                }
            } catch (error) {
                console.error('启动R2状态同步失败:', error);
            }
        }, 1000);
    }

    // 执行R2状态同步
    async performR2StatusSync(pendingTasks) {
        console.log(`🔄 开始同步 ${pendingTasks.length} 个待处理任务的状态...`);

        let updatedCount = 0;
        const completedTasks = [];

        for (const task of pendingTasks) {
            try {
                const updated = await this.syncSingleTaskStatus(task.id);
                if (updated) {
                    updatedCount++;

                    // 检查任务是否已完成
                    const updatedTask = this.allTasks.find(t => t.id === task.id);
                    if (updatedTask && updatedTask.status === 'completed') {
                        completedTasks.push(updatedTask);
                    }
                }
            } catch (error) {
                console.error(`同步任务 ${task.id} 状态失败:`, error);
            }
        }

        if (updatedCount > 0) {
            console.log(`✅ 成功同步了 ${updatedCount} 个任务的状态`);

            // 重新渲染任务列表
            this.renderMyTasks();

            // 显示完成通知
            if (completedTasks.length > 0) {
                completedTasks.forEach(task => {
                    this.showMessage(`🎵 任务"${task.filename}"已完成！`, 'success');
                });
            }
        }
    }

    // 增强版：页面加载时主动状态同步验证
    async enhancedAutoVerifyTasksOnLoad() {
        // 延迟验证，避免影响页面渲染
        setTimeout(async () => {
            try {
                // 检查所有非最终状态的任务
                const unfinishedTasks = this.allTasks.filter(task => {
                    const isNonFinalStatus = !['completed', 'failed'].includes(task.status);

                    // 额外检查：处理中但时间较长的任务（可能已完成但状态未更新）
                    const isLongProcessingTask = task.status === 'pending' &&
                        task.createdAt &&
                        (Date.now() - new Date(task.createdAt).getTime()) > 10 * 60 * 1000; // 超过10分钟

                    return isNonFinalStatus || isLongProcessingTask;
                });

                if (unfinishedTasks.length > 0) {
                    console.log(`🔍 页面加载时主动验证 ${unfinishedTasks.length} 个未完成任务的状态...`);

                    // 使用专门的自动同步API进行更全面的检查
                    const syncResponse = await fetch('/api/auto-sync-task-status', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email: this.user.email,
                            autoMode: true,
                            checkAll: true  // 新增：检查所有未完成任务，不仅仅是卡住的任务
                        })
                    });

                    if (syncResponse.ok) {
                        const syncResult = await syncResponse.json();
                        console.log(`✅ 主动状态同步完成:`, syncResult.summary);

                        if (syncResult.summary.updatedTasks > 0) {
                            // 重新加载任务列表以显示最新状态
                            await this.loadMyTasks();

                            // 显示同步结果
                            this.showMessage(`🎉 发现并修复了 ${syncResult.summary.updatedTasks} 个任务的状态！`, 'success');
                        }
                    } else {
                        console.warn('主动状态同步失败，回退到传统方法');
                        await this.syncTasksStatus();
                    }
                } else {
                    console.log('✅ 所有任务都已完成，无需状态同步');
                }
            } catch (error) {
                console.warn('增强版自动验证任务状态失败:', error);
                // 回退到原有的同步方法
                await this.syncTasksStatus();
            }
        }, 1500); // 缩短延迟时间，更快响应
    }

    // 混合方案：基于R2完成标记的状态同步
    async hybridTaskStatusSync() {
        // 延迟执行，避免影响页面渲染
        setTimeout(async () => {
            try {
                if (!this.user || !this.allTasks || this.allTasks.length === 0) {
                    console.log('🚫 没有任务需要同步');
                    return;
                }

                // 找出所有未完成的任务
                const unfinishedTasks = this.allTasks.filter(task =>
                    !['completed', 'failed'].includes(task.status)
                );

                if (unfinishedTasks.length === 0) {
                    console.log('✅ 所有任务都已完成，无需状态同步');
                    return;
                }

                console.log(`🔄 使用混合方案同步 ${unfinishedTasks.length} 个未完成任务的状态...`);

                // 调用新的增强版状态同步API
                const syncResponse = await fetch('/api/sync-task-status-enhanced', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: this.user.email,
                        autoMode: true
                    })
                });

                if (syncResponse.ok) {
                    const syncResult = await syncResponse.json();
                    console.log(`✅ 混合方案状态同步完成:`, syncResult.summary);

                    if (syncResult.summary.updatedTasks > 0) {
                        // 重新加载任务列表以显示最新状态
                        await this.loadMyTasks();

                        // 显示同步结果
                        this.showMessage(`🎉 发现并修复了 ${syncResult.summary.updatedTasks} 个任务的状态！`, 'success');

                        // 为每个完成的任务显示通知
                        syncResult.summary.completedTasks.forEach(task => {
                            this.showMessage(`🎵 任务"${task.filename}"已完成！`, 'success');
                        });
                    } else {
                        console.log('📊 所有任务状态都是最新的');
                    }
                } else {
                    console.warn('混合方案同步失败，回退到传统方法');
                    await this.enhancedAutoVerifyTasksOnLoad();
                }

            } catch (error) {
                console.warn('混合方案状态同步失败:', error);
                // 回退到原有的同步方法
                await this.enhancedAutoVerifyTasksOnLoad();
            }
        }, 1000); // 1秒延迟，比原来的方法更快
    }

    // R2架构的状态同步方法
    async r2TaskStatusSync() {
        // 延迟执行，避免影响页面渲染
        setTimeout(async () => {
            try {
                if (!this.user || !this.allTasks || this.allTasks.length === 0) {
                    console.log('🚫 没有任务需要同步 (R2)');
                    return;
                }

                // 找出所有未完成的任务
                const unfinishedTasks = this.allTasks.filter(task =>
                    !['completed', 'failed'].includes(task.status)
                );

                if (unfinishedTasks.length === 0) {
                    console.log('✅ 所有任务都已完成，无需状态同步 (R2)');
                    return;
                }

                console.log(`🔄 使用R2架构同步 ${unfinishedTasks.length} 个未完成任务的状态...`);

                // 批量获取任务状态
                const taskIds = unfinishedTasks.map(task => task.id);
                const batchResponse = await fetch('/api/tasks-r2', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: this.user.email,
                        taskIds: taskIds,
                        architecture: 'both'
                    })
                });

                if (batchResponse.ok) {
                    const batchResult = await batchResponse.json();
                    console.log(`✅ R2批量状态查询完成:`, batchResult);

                    let updatedCount = 0;
                    const completedTasks = [];

                    // 检查每个任务的状态变化
                    batchResult.results.forEach(result => {
                        if (result.status) {
                            const localTask = this.allTasks.find(t => t.id === result.taskId);
                            if (localTask && localTask.status !== result.status.status) {
                                // 更新本地任务状态
                                Object.assign(localTask, result.status);
                                updatedCount++;

                                if (result.status.status === 'completed') {
                                    completedTasks.push(result.status);
                                }
                            }
                        }
                    });

                    if (updatedCount > 0) {
                        // 重新渲染任务列表
                        this.renderMyTasks();

                        // 显示同步结果
                        this.showMessage(`🎉 发现并更新了 ${updatedCount} 个任务的状态！`, 'success');

                        // 为每个完成的任务显示通知
                        completedTasks.forEach(task => {
                            this.showMessage(`🎵 任务"${task.filename}"已完成！`, 'success');
                        });
                    } else {
                        console.log('📊 所有任务状态都是最新的 (R2)');
                    }
                } else {
                    console.warn('R2状态同步失败，回退到混合方案');
                    await this.hybridTaskStatusSync();
                }

            } catch (error) {
                console.warn('R2状态同步失败:', error);
                // 回退到混合方案
                await this.hybridTaskStatusSync();
            }
        }, 800); // 比混合方案更快
    }

    // 新增：同步任务状态（改进版）
    async syncTasksStatus() {
        if (!this.user || !this.allTasks || this.allTasks.length === 0) {
            console.log('🚫 没有任务需要同步');
            return;
        }

        // 找出需要同步的任务（处理中的任务）
        const tasksToSync = this.allTasks.filter(task =>
            task.status === 'pending'
        );

        if (tasksToSync.length === 0) {
            console.log('✅ 所有任务都已完成，无需同步');
            return;
        }

        console.log(`🔄 开始同步 ${tasksToSync.length} 个任务的状态...`);

        try {
            // 首先使用新的验证API检查音频文件是否存在
            const taskIds = tasksToSync.map(task => task.id);
            const verifyResponse = await fetch('/api/verify-task-completion', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    email: this.user.email,
                    taskIds: taskIds
                })
            });

            if (verifyResponse.ok) {
                const verifyResult = await verifyResponse.json();
                console.log(`📊 验证结果:`, verifyResult);

                // 如果有任务状态被更新，重新加载任务列表
                if (verifyResult.totalUpdated > 0) {
                    console.log(`🎉 ${verifyResult.totalUpdated} 个任务状态已更新！`);
                    
                    // 重新加载任务数据
                    await this.loadMyTasks();
                    
                    // 显示成功通知
                    this.showMessage(`✅ 已同步 ${verifyResult.totalUpdated} 个任务的最新状态`, 'success');
                    
                    // 为每个完成的任务显示单独通知
                    verifyResult.verificationResults.forEach(result => {
                        if (result.updated && result.newStatus === 'completed') {
                            const task = tasksToSync.find(t => t.id === result.taskId);
                            if (task) {
                                this.showMessage(`🎵 任务"${task.filename || task.title}"已完成！`, 'success');
                            }
                        }
                    });
                    
                    return;
                }
            } else {
                console.warn('音频文件验证失败，使用传统状态检查');
            }

            // 如果验证API失败，回退到传统的状态检查
            let syncCount = 0;
            const syncPromises = tasksToSync.map(async (task) => {
                const updated = await this.syncSingleTaskStatus(task.id);
                if (updated) syncCount++;
                return updated;
            });

            await Promise.all(syncPromises);

            if (syncCount > 0) {
                this.showMessage(`✅ 已同步 ${syncCount} 个任务的最新状态`, 'success');
                
                // 重新渲染任务列表以显示最新状态
                this.renderMyTasks();
            } else {
                console.log('📊 所有任务状态都是最新的');
            }

        } catch (error) {
            console.error('同步任务状态失败:', error);
            this.showMessage('同步任务状态失败，请稍后重试', 'error');
        }
    }

    // 新增：同步单个任务的状态（直接从R2读取）
    async syncSingleTaskStatus(taskId) {
        try {
            console.log(`🔍 直接从R2同步任务状态: ${taskId}`);

            // 创建R2状态读取器
            const statusReader = new R2StatusReader(taskId, this.user.email);

            // 直接从R2读取状态文件
            const statusData = await statusReader.readStatusFromR2();

            console.log(`📊 任务 ${taskId} 最新状态:`, statusData);
            
            // 检查状态是否有变化
            const localTask = this.allTasks.find(task => task.id === taskId);
            if (!localTask) return false;
            
            const hasStatusChange = localTask.status !== statusData.status;
            const hasProgressChange = localTask.progress !== statusData.progress;
            
            if (hasStatusChange || hasProgressChange) {
                console.log(`🔄 任务 ${taskId} 状态已更新: ${localTask.status} → ${statusData.status}`);
                
                // 更新本地数据
                this.updateLocalTaskStatus(taskId, statusData);
                
                // 如果任务完成，显示通知
                if (this.isTaskCompleted(statusData.status) && hasStatusChange) {
                    this.showMessage(`✅ 任务"${localTask.filename || localTask.title}"已完成！`, 'success');
                }
                
                return true;
            }
            
            return false;
        } catch (error) {
            console.warn(`同步任务 ${taskId} 状态失败:`, error);
            return false;
        }
    }

    renderMyTasks(filter = 'all') {
        const myTasksList = document.getElementById('my-tasks-list');
        
        let filteredTasks = this.allTasks || [];
        if (filter !== 'all') {
            filteredTasks = filteredTasks.filter(task => task.status === filter);
        }

        if (filteredTasks.length === 0) {
            const message = filter === 'all' ? '您还没有提交任何任务' : `没有找到状态为"${this.getStatusText(filter)}"的任务`;
            myTasksList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-tasks"></i>
                    <h3>暂无任务</h3>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="app.switchPage('home')">
                        <i class="fas fa-plus"></i> 创建新任务
                    </button>
                </div>
            `;
            return;
        }

        myTasksList.innerHTML = filteredTasks.map(task => `
            <div class="my-task-item status-${task.status.toLowerCase()} fade-in" data-task-id="${task.id}">
                <div class="task-header">
                    <div class="task-title">
                        <h3>${task.title || task.filename || '未知任务'}</h3>
                        <div class="task-meta">
                            <span><i class="fas fa-calendar"></i> ${task.createdAtFormatted}</span>
                            <span><i class="fas fa-tag"></i> ${task.typeDisplay}</span>
                            ${task.audioSizeFormatted ? `<span><i class="fas fa-file-audio"></i> ${task.audioSizeFormatted}</span>` : ''}
                        </div>
                    </div>
                    <div class="task-status-badge ${task.status.toLowerCase()}">
                        ${this.getStatusText(task.status)}
                    </div>
                </div>

                ${task.progress !== undefined && task.status === 'pending' ? `
                    <div class="task-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${task.progress}%"></div>
                        </div>
                        <p style="text-align: center; margin-top: 0.5rem; color: #666; font-size: 0.9rem;">
                            处理进度: ${task.progress}%
                        </p>
                    </div>
                ` : ''}

                ${task.error ? `
                    <div style="background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 6px; margin-top: 1rem;">
                        <i class="fas fa-exclamation-circle"></i> 错误信息: ${task.error}
                    </div>
                ` : ''}

                ${task.status === 'completed' && task.audioUrl ? `
                    <div class="task-actions" style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button class="btn btn-primary btn-sm" onclick="app.playAudioBook('${task.id}')" style="flex: 1; min-width: 120px;">
                            <i class="fas fa-play"></i> 播放音频
                        </button>
                        ${task.downloadUrl || task.audioUrl ? `
                            <a href="${task.downloadUrl || task.audioUrl}" class="btn btn-outline btn-sm" download style="flex: 1; min-width: 120px; text-decoration: none; display: inline-flex; align-items: center; justify-content: center;">
                                <i class="fas fa-download"></i> 下载音频
                            </a>
                        ` : ''}
                        ${task.playlistUrl ? `
                            <a href="${task.playlistUrl}" class="btn btn-outline btn-sm" target="_blank" style="flex: 1; min-width: 120px; text-decoration: none; display: inline-flex; align-items: center; justify-content: center;">
                                <i class="fas fa-list"></i> 播放列表
                            </a>
                        ` : ''}
                    </div>
                ` : ''}
            </div>
        `).join('');
        
        console.log('我的任务列表渲染完成，任务数量:', filteredTasks.length);
    }

    // 任务过滤
    filterTasks(status) {
        // 更新过滤按钮状态
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-status="${status}"]`).classList.add('active');
        
        // 渲染过滤后的任务
        this.renderMyTasks(status);
    }

    // 积分记录页面
    async loadPointsHistory() {
        if (!this.user) return;

        const pointsStats = document.getElementById('points-stats');
        const pointsHistory = document.getElementById('points-history');
        
        pointsHistory.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在加载积分记录...</div>';

        try {
            const response = await fetch('/api/auth/points', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });

            const result = await response.json();

            if (response.ok) {
                console.log('积分API响应数据:', result);
                console.log('stats数据:', result.stats);
                this.renderPointsStats(result.stats);
                this.renderPointsHistory(result.records);
            } else {
                pointsHistory.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>请刷新页面重试</p></div>';
            }
        } catch (error) {
            console.error('加载积分记录失败:', error);
            pointsHistory.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>请刷新页面重试</p></div>';
        }
    }

    renderPointsStats(stats) {
        console.log('renderPointsStats 接收到的数据:', stats);

        const pointsStats = document.getElementById('points-stats');
        if (!pointsStats) {
            console.error('找不到 points-stats 元素');
            return;
        }

        if (!stats) {
            console.error('stats 数据为空');
            pointsStats.innerHTML = '<div class="error">积分数据加载失败</div>';
            return;
        }

        pointsStats.innerHTML = `
            <div class="stat-card current-balance">
                <i class="fas fa-wallet"></i>
                <div class="stat-number">${stats.currentBalance || 0}</div>
                <div class="stat-label">当前余额</div>
            </div>
            <div class="stat-card total-earned">
                <i class="fas fa-plus-circle"></i>
                <div class="stat-number">${stats.totalEarned || 0}</div>
                <div class="stat-label">累计获得</div>
            </div>
            <div class="stat-card total-consumed">
                <i class="fas fa-minus-circle"></i>
                <div class="stat-number">${stats.totalConsumed || 0}</div>
                <div class="stat-label">累计消费</div>
            </div>
            <div class="stat-card total-records">
                <i class="fas fa-list"></i>
                <div class="stat-number">${stats.totalRecords || 0}</div>
                <div class="stat-label">总记录数</div>
            </div>
        `;
    }

    renderPointsHistory(records) {
        const pointsHistory = document.getElementById('points-history');
        
        if (records.length === 0) {
            pointsHistory.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-coins"></i>
                    <h3>暂无积分记录</h3>
                    <p>开始使用服务后，您的积分变动记录将在这里显示</p>
                    <button class="btn btn-primary" onclick="app.switchPage('home')">
                        <i class="fas fa-upload"></i> 开始转换
                    </button>
                </div>
            `;
            return;
        }

        pointsHistory.innerHTML = records.map(record => `
            <div class="points-record fade-in">
                <div class="record-info">
                    <h4>${record.description}</h4>
                    <p>${record.createdAtFormatted}</p>
                </div>
                <div class="record-amount">
                    <div class="amount-change ${record.colorClass}">
                        ${record.amountDisplay}
                    </div>
                    <div class="balance-after">
                        余额: ${record.balance}
                    </div>
                </div>
            </div>
        `).join('');
    }

    getStatusIcon(status) {
        const icons = {
            'pending': 'fa-spinner spinning',
            'completed': 'fa-check-circle',
            'failed': 'fa-exclamation-circle'
        };
        return icons[status] || 'fa-question-circle';
    }

    getStatusClass(status) {
        const classes = {
            'pending': 'status-pending',
            'completed': 'status-success',
            'failed': 'status-failed'
        };
        return classes[status] || '';
    }

    getStatusText(status) {
        switch(status) {
            case 'pending': return '处理中';
            case 'completed': return '已完成';
            case 'failed': return '失败';
            default: return '未知';
        }
    }

    // 音频库功能
    async loadAudioLibrary(sortBy = 'recent') {
        if (!this.user) return;

        const audioList = document.getElementById('audio-library-list');
        audioList.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在加载音频库...</div>';

        try {
            // 使用专门的音频库API获取所有有音频的任务，添加认证令牌
            const response = await fetch(`/api/audio-library`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            const result = await response.json();

            if (response.ok) {
                console.log('音频库加载结果:', result.statistics);

                // 显示统计信息（如果有可能需要修复的任务）
                if (result.potentialFixTasks && result.potentialFixTasks.length > 0) {
                    console.warn('发现可能需要修复的任务:', result.potentialFixTasks);
                    this.showPotentialFixMessage(result.potentialFixTasks.length);
                }

                this.renderAudioLibrary(result.audioBooks, sortBy, result.statistics);
            } else {
                // 如果新API失败，回退到旧方式
                console.warn('新音频库API失败，回退到旧方式:', result);
                await this.loadAudioLibraryFallback(sortBy);
            }
        } catch (error) {
            console.error('加载音频库失败:', error);
            // 尝试回退到旧方式
            await this.loadAudioLibraryFallback(sortBy);
        }
    }

    // 回退的音频库加载方法（使用原有的tasks API但增加参数）
    async loadAudioLibraryFallback(sortBy = 'recent') {
        const audioList = document.getElementById('audio-library-list');
        audioList.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在加载音频库（回退模式）...</div>';

        try {
            // 使用更大的任务限制
            const response = await fetch(`/api/tasks`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            const result = await response.json();

            if (response.ok) {
                // 使用更宽松的过滤条件
                const audioBooks = result.filter(task => {
                    if (task.status !== 'completed') return false;
                    
                    // 检查多种可能的音频字段
                    const hasAudioUrl = !!task.audioUrl;
                    const hasPlaylistUrl = !!task.playlistUrl;
                    const hasAudioPath = !!task.audioPath;
                    const isEnhanced = task.isEnhanced || task.totalChapters > 1;
                    
                    // 如果是增强版，只要有playlistUrl就算有音频
                    if (isEnhanced && hasPlaylistUrl) {
                        return true;
                    }
                    
                    // 普通任务需要有audioUrl或audioPath
                    return hasAudioUrl || hasAudioPath;
                });
                
                console.log(`回退模式：从 ${result.length} 个任务中找到 ${audioBooks.length} 个音频书籍`);
                this.renderAudioLibrary(audioBooks, sortBy);
            } else {
                audioList.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>请刷新页面重试</p></div>';
            }
        } catch (error) {
            console.error('回退模式也失败了:', error);
            audioList.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>请刷新页面重试</p></div>';
        }
    }

    // 显示可能需要修复任务的提示
    showPotentialFixMessage(count) {
        // 检查是否已经显示过提示
        if (document.getElementById('fix-tasks-hint')) return;
        
        const hint = document.createElement('div');
        hint.id = 'fix-tasks-hint';
        hint.className = 'fix-tasks-hint';
        hint.innerHTML = `
            <div class="hint-content">
                <i class="fas fa-info-circle"></i>
                <span>发现 ${count} 个可能缺少音频链接的任务，<a href="/test-fix.html" target="_blank">点击这里修复</a></span>
                <button onclick="this.parentElement.parentElement.remove()" class="hint-close">×</button>
            </div>
        `;
        
        const audioList = document.getElementById('audio-library-list');
        audioList.parentElement.insertBefore(hint, audioList);
    }

    renderAudioLibrary(audioBooks, sortBy = 'recent', statistics = null) {
        const container = document.getElementById('audio-library-list');
        if (!container) return;

        // 如果有统计信息，显示在顶部
        let statsHtml = '';
        if (statistics) {
            statsHtml = `
                <div class="library-stats">
                    <span class="stat-item">总任务: ${statistics.totalTasks}</span>
                    <span class="stat-item">成功任务: ${statistics.successTasks}</span>
                    <span class="stat-item">音频书籍: ${statistics.audioTasks}</span>
                    ${statistics.potentialFixTasks > 0 ? `<span class="stat-item warning">需修复: ${statistics.potentialFixTasks}</span>` : ''}
                </div>
            `;
        }

        if (!audioBooks || audioBooks.length === 0) {
            container.innerHTML = statsHtml + `
                <div class="empty-state">
                    <i class="fas fa-book-open"></i>
                    <h3>暂无音频书籍</h3>
                    <p>开始上传您的第一本书籍吧！</p>
                    ${statistics && statistics.potentialFixTasks > 0 ? '<p><a href="/test-fix.html" target="_blank">或者尝试修复现有任务</a></p>' : ''}
                </div>
            `;
            return;
        }

        const sortedBooks = this.sortAudioBooks(audioBooks, sortBy);
        
        container.innerHTML = statsHtml + sortedBooks.map(book => {
            const progress = this.getBookProgress(book.id) || { currentTime: 0, duration: 0, percentage: 0 };
            const duration = this.getAudioDuration(book, progress);

            // 修复进度百分比计算
            let progressPercent = 0;
            if (progress.percentage && !isNaN(progress.percentage)) {
                progressPercent = progress.percentage;
            } else if (progress.currentTime && progress.duration && progress.duration > 0) {
                progressPercent = (progress.currentTime / progress.duration) * 100;
            } else if (book.durationSeconds && progress.currentTime) {
                progressPercent = (progress.currentTime / book.durationSeconds) * 100;
            }

            // 确保进度百分比在有效范围内
            progressPercent = Math.max(0, Math.min(100, progressPercent || 0));
            
            // 检查是否为增强版音频书
            const isEnhanced = book.isEnhanced || book.chapters?.length > 1 || book.totalChapters > 1;
            const enhancedBadge = isEnhanced ? '<span class="mode-badge enhanced">增强版</span>' : '';
            const chapterInfo = isEnhanced && (book.chapters?.length || book.totalChapters) ? 
                `<span class="chapter-count">${book.chapters?.length || book.totalChapters} 章节</span>` : '';
            const enhancedClass = isEnhanced ? ' enhanced' : '';
            
            // 修复字段名称：使用正确的任务数据字段
            const bookTitle = book.title || book.filename || '未知标题';
            const bookDate = this.formatDate(book.createdAt || book.created_at);
            
            return `
                <div class="audio-book-item${enhancedClass}" data-book-id="${book.id}">
                    <div class="book-cover">
                        <i class="fas fa-book"></i>
                        ${enhancedBadge}
                    </div>
                    <div class="book-info">
                        <h3 class="book-title">${bookTitle}</h3>
                        <div class="book-meta">
                            <span class="book-date">${bookDate}</span>
                            ${chapterInfo}
                            <span class="book-duration">${duration}</span>
                        </div>
                        <div class="book-progress">
                            <div class="progress-label">播放进度: ${Math.round(progressPercent)}%</div>
                            <div class="progress-bar-mini">
                                <div class="progress-fill-mini" style="width: ${progressPercent}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="book-actions">
                        <button class="btn-play" onclick="app.playAudioBook('${book.id}')" title="播放">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn-options" onclick="app.showBookOptions('${book.id}')" title="选项">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 获取音频时长信息的辅助函数
    getAudioDuration(book, progress) {
        // 优先使用播放进度中保存的时长信息（最准确）
        if (progress && progress.duration > 0) {
            return this.formatTime(progress.duration);
        }
        
        // 对于增强版音频书，使用totalDuration字段
        if (book.isEnhanced && book.totalDuration && book.totalDuration > 0) {
            return this.formatTime(book.totalDuration);
        }
        
        // 其次使用书籍数据中的时长字段
        if (book.duration && book.duration > 0) {
            return this.formatTime(book.duration);
        }
        
        if (book.audioDuration && book.audioDuration > 0) {
            return this.formatTime(book.audioDuration);
        }
        
        // 如果有音频文件大小信息，估算时长（粗略估算：1MB ≈ 1分钟）
        if (book.audioSize && book.audioSize > 0) {
            const estimatedMinutes = Math.round(book.audioSize / (1024 * 1024));
            return `约${estimatedMinutes}分钟`;
        }
        
        if (book.fileSize && book.fileSize > 0) {
            const estimatedMinutes = Math.round(book.fileSize / (1024 * 1024));
            return `约${estimatedMinutes}分钟`;
        }
        
        // 对于增强版音频书，如果有章节信息，估算总时长
        if (book.isEnhanced && book.chapters && book.chapters.length > 0) {
            const totalDuration = book.chapters.reduce((sum, chapter) => sum + (chapter.duration || 0), 0);
            if (totalDuration > 0) {
                return this.formatTime(totalDuration);
            }
        }
        
        // 如果都没有，返回未知
        return '时长未知';
    }

    // 新增：音频书籍排序函数
    sortAudioBooks(audioBooks, sortBy) {
        const books = [...audioBooks]; // 创建副本以避免修改原数组
        
        switch (sortBy) {
            case 'name':
                // 按名称排序（字母顺序）- 修复字段名称
                return books.sort((a, b) => {
                    const nameA = (a.title || a.filename || '未命名书籍').toLowerCase();
                    const nameB = (b.title || b.filename || '未命名书籍').toLowerCase();
                    return nameA.localeCompare(nameB, 'zh-CN');
                });
                
            case 'duration':
                // 按时长排序（从长到短），如果没有时长信息则按文件大小
                return books.sort((a, b) => {
                    // 首先尝试使用增强版音频书的totalDuration字段
                    let durationA = 0;
                    let durationB = 0;
                    
                    // 获取音频书A的时长
                    if (a.isEnhanced && a.totalDuration) {
                        durationA = a.totalDuration;
                    } else if (a.duration || a.audioDuration) {
                        durationA = a.duration || a.audioDuration;
                    } else if (a.isEnhanced && a.chapters && a.chapters.length > 0) {
                        durationA = a.chapters.reduce((sum, chapter) => sum + (chapter.duration || 0), 0);
                    }
                    
                    // 获取音频书B的时长
                    if (b.isEnhanced && b.totalDuration) {
                        durationB = b.totalDuration;
                    } else if (b.duration || b.audioDuration) {
                        durationB = b.duration || b.audioDuration;
                    } else if (b.isEnhanced && b.chapters && b.chapters.length > 0) {
                        durationB = b.chapters.reduce((sum, chapter) => sum + (chapter.duration || 0), 0);
                    }
                    
                    // 如果沒有時長信息，使用文件大小作為替代排序依據
                    if (durationA === 0 && durationB === 0) {
                        durationA = a.totalSize || a.audioSize || a.fileSize || 0;
                        durationB = b.totalSize || b.audioSize || b.fileSize || 0;
                    }
                    
                    return durationB - durationA; // 降序排列
                });
                
            case 'recent':
            default:
                // 按最近添加排序（最新的在前）- 修复字段名称
                return books.sort((a, b) => {
                    const dateA = new Date(a.createdAt || a.created_at || 0);
                    const dateB = new Date(b.createdAt || b.created_at || 0);
                    return dateB - dateA; // 降序排列
                });
        }
    }

    filterLibrary(sortBy) {
        // 保存當前排序方式
        this.currentLibrarySort = sortBy;
        
        // 更新过滤按钮状态
        document.querySelectorAll('.library-filter-btn').forEach(btn => btn.classList.remove('active'));
        const activeBtn = document.querySelector(`[data-sort="${sortBy}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
        
        // 实现排序逻辑：重新加载并排序音频库
        this.loadAudioLibrary(sortBy);
    }

    // 播放音频书籍
    async playAudioBook(bookId) {
        try {
            // 首先从音频库中找到对应的书籍数据
            const audioBooks = await this.getAudioBooksData();
            // 修复ID类型匹配问题：确保比较时类型一致
            this.currentBook = audioBooks.find(book => book.id == bookId || book.id === parseInt(bookId));
            
            if (!this.currentBook) {
                this.showMessage('找不到指定的音频书籍', 'error');
                return;
            }
            
            // 检查任务数据中是否标记为增强版
            const isEnhancedFromTask = this.currentBook.isEnhanced || 
                                     this.currentBook.audioType === 'MULTI_CHAPTER' ||
                                     this.currentBook.processingType === 'MULTI_CHAPTER' ||
                                     this.currentBook.totalChapters > 1 ||
                                     (this.currentBook.audioUrl && this.currentBook.audioUrl.includes('/playlist/'));
            
            // 如果任务标记为增强版，直接使用增强版播放器
            if (isEnhancedFromTask) {
                console.log('检测到增强版音频书，使用增强版播放器');
                this.switchPage('enhanced-audio-player-page');
                this.initEnhancedPlayer(bookId);
                return;
            }
            
            // 尝试检查元数据API来确认是否为增强版
            try {
                const metadataResponse = await fetch(`/api/audio/metadata/${bookId}`, {
                    headers: {
                        'X-User-Email': this.user.email
                    }
                });
                
                if (metadataResponse.ok) {
                    const metadata = await metadataResponse.json();
                    if (metadata.isEnhanced && metadata.chapters && metadata.chapters.length > 1) {
                        // 使用增强版播放器
                        console.log('通过元数据API确认为增强版音频书');
                        this.switchPage('enhanced-audio-player-page');
                        this.initEnhancedPlayer(bookId);
                        return;
                    }
                }
            } catch (error) {
                console.warn('元数据API调用失败，继续使用传统播放器:', error);
            }
            
            // 使用传统播放器
            this.switchPage('audio-player-page');

            // 确保音频播放器HTML结构存在
            this.ensureAudioPlayerHTML();

            const audioElement = document.getElementById('audio-element');
            
            // 对于传统音频书，使用正确的音频URL
            let audioUrl;
            if (this.currentBook.audioUrl && !this.currentBook.audioUrl.includes('/playlist/')) {
                // 如果有audioUrl且不是播放列表URL，直接使用
                audioUrl = this.currentBook.audioUrl;
            } else {
                // 使用新的音频流API
                audioUrl = `/api/audio/${bookId}/stream`;
            }
            
            audioElement.src = audioUrl;
            console.log('设置传统播放器音频源:', audioUrl);
            
            // 恢复播放进度
            const progress = this.getBookProgress(bookId);
            if (progress > 0) {
                audioElement.currentTime = progress;
            }
            
            this.currentBookId = bookId;
            this.setupAudioPlayer();
            
        } catch (error) {
            console.error('播放音频失败:', error);
            this.showMessage('播放失败，请重试', 'error');
        }
    }

    // 新增：获取音频书籍数据的辅助方法
    async getAudioBooksData() {
        try {
            // 首先尝试使用新的音频库API
            const response = await fetch(`/api/audio-library`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            const result = await response.json();
            
            if (response.ok && result.audioBooks) {
                return result.audioBooks;
            } else {
                // 回退到旧的tasks API
                console.warn('音频库API失败，回退到tasks API');
                const tasksResponse = await fetch(`/api/tasks`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    }
                });
                const tasksResult = await tasksResponse.json();

                if (tasksResponse.ok) {
                    // 使用更宽松的过滤条件
                    return tasksResult.filter(task => {
                        if (task.status !== 'completed') return false;
                        
                        // 检查多种可能的音频字段
                        const hasAudioUrl = !!task.audioUrl;
                        const hasPlaylistUrl = !!task.playlistUrl;
                        const hasAudioPath = !!task.audioPath;
                        const isEnhanced = task.isEnhanced || task.totalChapters > 1;
                        
                        // 如果是增强版，只要有playlistUrl就算有音频
                        if (isEnhanced && hasPlaylistUrl) {
                            return true;
                        }
                        
                        // 普通任务需要有audioUrl或audioPath
                        return hasAudioUrl || hasAudioPath;
                    });
                }
            }
            return [];
        } catch (error) {
            console.error('获取音频书籍数据失败:', error);
            return [];
        }
    }

    initEnhancedPlayer(bookId) {
        try {
            // 确保增强版播放器容器存在
            const container = document.getElementById('enhanced-player-container');
            if (!container) {
                console.error('Enhanced player container not found');
                this.showMessage('播放器初始化失败，使用传统播放器', 'warning');
                this.switchPage('audio-player-page');
                return;
            }

            // 销毁现有播放器实例
            if (this.enhancedPlayer) {
                this.enhancedPlayer.destroy();
            }

            // 创建新的增强版播放器实例
            this.enhancedPlayer = new EnhancedAudioPlayer('enhanced-player-container', {
                autoPlay: false,
                showChapters: true,
                enableKeyboardShortcuts: true
            });

            // 加载音频书
            this.enhancedPlayer.loadBook(bookId).catch(error => {
                console.error('Enhanced player load failed:', error);
                this.showMessage('增强版播放器加载失败，请重试', 'error');
            });
            
            this.currentBookId = bookId;
        } catch (error) {
            console.error('Enhanced player initialization failed:', error);
            this.showMessage('播放器初始化失败', 'error');
        }
    }

    // 设置音频播放器
    setupAudioPlayer() {
        if (!this.currentBook) {
            console.error('当前书籍数据不存在');
            this.showMessage('书籍数据加载失败，请重试', 'error');
            return;
        }

        // 更新书籍信息
        const titleElement = document.getElementById('current-book-title');
        const metaElement = document.getElementById('current-book-meta');
        
        if (titleElement) {
            titleElement.textContent = this.currentBook.title || this.currentBook.filename || '未命名书籍';
        }
        
        if (metaElement) {
            const bookType = this.currentBook.type === 'file' ? 'EPUB/TXT' : '网页';
            const bookDate = this.formatDate(this.currentBook.createdAt || this.currentBook.created_at);
            metaElement.textContent = `${bookType} • ${bookDate}`;
        }

        // 设置音频元素
        const audioElement = document.getElementById('audio-element');
        if (!audioElement) {
            console.error('音频元素不存在');
            this.showMessage('播放器初始化失败，请刷新页面重试', 'error');
            return;
        }
        
        // 优化音频加载设置
        audioElement.preload = 'metadata'; // 预加载元数据
        audioElement.crossOrigin = 'anonymous'; // 处理跨域问题
        
        // 设置音频源 - 使用正确的音频URL
        const audioUrl = this.currentBook.audioUrl || `/api/audio/${this.currentBook.id}/stream`;
        audioElement.src = audioUrl;
        console.log('设置音频源:', audioUrl);

        // 添加音频加载状态监听
        this.setupAudioLoadingHandlers(audioElement);

        // 恢复播放进度（等待音频准备好后再设置）
        const progress = this.getBookProgress(this.currentBook.id);
        if (progress.currentTime > 0) {
            // 等待音频元数据加载完成后再设置播放位置
            const setInitialTime = () => {
                if (audioElement.duration && audioElement.readyState >= 1) {
                    this.seekAudio(audioElement, progress.currentTime);
                } else {
                    // 如果还没准备好，继续等待
                    setTimeout(setInitialTime, 100);
                }
            };
            setInitialTime();
        }

        // 绑定播放器事件
        this.setupPlayerEvents();
        
        // 加载书签
        this.loadBookmarks();
    }

    // 新增：音频加载状态处理
    setupAudioLoadingHandlers(audioElement) {
        // 显示加载状态
        this.showMessage('正在加载音频...', 'info');
        
        // 音频开始加载
        audioElement.addEventListener('loadstart', () => {
            console.log('音频开始加载');
        });

        // 音频元数据加载完成
        audioElement.addEventListener('loadedmetadata', () => {
            console.log('音频元数据加载完成，时长:', this.formatTime(audioElement.duration));
            document.getElementById('total-time').textContent = this.formatTime(audioElement.duration);
        });

        // 音频数据开始加载
        audioElement.addEventListener('loadeddata', () => {
            console.log('音频数据开始加载');
        });

        // 音频可以开始播放
        audioElement.addEventListener('canplay', () => {
            console.log('音频可以开始播放');
            this.showMessage('音频加载完成', 'success');
        });

        // 音频可以流畅播放
        audioElement.addEventListener('canplaythrough', () => {
            console.log('音频可以流畅播放');
        });

        // 音频缓冲进度
        audioElement.addEventListener('progress', () => {
            if (audioElement.buffered.length > 0) {
                const bufferedEnd = audioElement.buffered.end(audioElement.buffered.length - 1);
                const duration = audioElement.duration;
                if (duration > 0) {
                    const bufferedPercent = (bufferedEnd / duration) * 100;
                    console.log(`音频缓冲进度: ${bufferedPercent.toFixed(1)}%`);
                    
                    // 如果缓冲进度较低，给用户提示
                    if (bufferedPercent < 10) {
                        console.log('音频缓冲中，跳转功能可能受限');
                    }
                }
            }
        });

        // 音频等待数据
        audioElement.addEventListener('waiting', () => {
            console.log('音频等待数据中...');
            this.showMessage('音频缓冲中，请稍候...', 'info');
        });

        // 音频可以继续播放
        audioElement.addEventListener('playing', () => {
            console.log('音频开始播放');
        });

        // 音频暂停
        audioElement.addEventListener('pause', () => {
            console.log('音频已暂停');
        });

        // 音频播放结束
        audioElement.addEventListener('ended', () => {
            console.log('音频播放结束');
            const playPauseBtn = document.getElementById('play-pause-btn');
            if (playPauseBtn) {
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            }
        });

        // 音频加载错误
        audioElement.addEventListener('error', (e) => {
            console.error('音频加载错误:', e);
            let errorMessage = '音频加载失败';
            
            if (audioElement.error) {
                switch (audioElement.error.code) {
                    case MediaError.MEDIA_ERR_ABORTED:
                        errorMessage = '音频加载被中断';
                        break;
                    case MediaError.MEDIA_ERR_NETWORK:
                        errorMessage = '网络错误，请检查网络连接';
                        break;
                    case MediaError.MEDIA_ERR_DECODE:
                        errorMessage = '音频解码失败';
                        break;
                    case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
                        errorMessage = '不支持的音频格式';
                        break;
                    default:
                        errorMessage = '未知的音频错误';
                }
            }
            
            this.showMessage(errorMessage, 'error');
        });

        // 音频跳转开始
        audioElement.addEventListener('seeking', () => {
            console.log('音频跳转中...');
        });

        // 音频跳转完成
        audioElement.addEventListener('seeked', () => {
            console.log('音频跳转完成，当前时间:', this.formatTime(audioElement.currentTime));
        });
    }

    setupPlayerEvents() {
        const audioElement = document.getElementById('audio-element');
        const playPauseBtn = document.getElementById('play-pause-btn');
        const rewindBtn = document.getElementById('rewind-btn');
        const forwardBtn = document.getElementById('forward-btn');
        const progressContainer = document.querySelector('.progress-bar-container');
        const progressFill = document.getElementById('progress-bar-fill');
        const progressHandle = document.getElementById('progress-handle');
        const currentTimeEl = document.getElementById('current-time');
        const totalTimeEl = document.getElementById('total-time');
        const speedSelect = document.getElementById('playback-speed');
        const volumeSlider = document.getElementById('volume-slider');
        const addBookmarkBtn = document.getElementById('add-bookmark-btn');
        // 注释掉强制跳转按钮的获取，因为该按钮已被隐藏
        // const forceSeekBtn = document.getElementById('force-seek-btn');

        // 检查所有必要的元素是否存在
        if (!audioElement || !playPauseBtn || !rewindBtn || !forwardBtn || !progressContainer || 
            !progressFill || !progressHandle || !currentTimeEl || !totalTimeEl || 
            !speedSelect || !volumeSlider || !addBookmarkBtn) {
            console.error('音频播放器的一些必要元素缺失');
            this.showMessage('播放器初始化失败，请刷新页面重试', 'error');
            return;
        }

        // 清除之前的事件监听器（避免重复绑定）
        const newPlayPauseBtn = playPauseBtn.cloneNode(true);
        playPauseBtn.parentNode.replaceChild(newPlayPauseBtn, playPauseBtn);
        
        const newRewindBtn = rewindBtn.cloneNode(true);
        rewindBtn.parentNode.replaceChild(newRewindBtn, rewindBtn);
        
        const newForwardBtn = forwardBtn.cloneNode(true);
        forwardBtn.parentNode.replaceChild(newForwardBtn, forwardBtn);

        const newAddBookmarkBtn = addBookmarkBtn.cloneNode(true);
        addBookmarkBtn.parentNode.replaceChild(newAddBookmarkBtn, addBookmarkBtn);

        // 注释掉强制跳转按钮的事件监听器清除，因为该按钮已被隐藏
        // const newForceSeekBtn = forceSeekBtn.cloneNode(true);
        // forceSeekBtn.parentNode.replaceChild(newForceSeekBtn, forceSeekBtn);

        // 重新获取替换后的元素
        const playPauseBtnNew = document.getElementById('play-pause-btn');
        const rewindBtnNew = document.getElementById('rewind-btn');
        const forwardBtnNew = document.getElementById('forward-btn');
        const addBookmarkBtnNew = document.getElementById('add-bookmark-btn');
        // 注释掉强制跳转按钮的重新获取，因为该按钮已被隐藏
        // const forceSeekBtnNew = document.getElementById('force-seek-btn');

        // 播放/暂停
        playPauseBtnNew.addEventListener('click', () => {
            if (audioElement.paused) {
                audioElement.play().catch(error => {
                    console.error('播放失败:', error);
                    this.showMessage('播放失败，请重试', 'error');
                });
                playPauseBtnNew.innerHTML = '<i class="fas fa-pause"></i>';
            } else {
                audioElement.pause();
                playPauseBtnNew.innerHTML = '<i class="fas fa-play"></i>';
            }
        });

        // 改进的快退/快进功能
        rewindBtnNew.addEventListener('click', () => {
            this.seekAudio(audioElement, Math.max(0, audioElement.currentTime - 10));
        });

        forwardBtnNew.addEventListener('click', () => {
            this.seekAudio(audioElement, Math.min(audioElement.duration || 0, audioElement.currentTime + 10));
        });

        // 改进的进度条拖拽功能
        let isDragging = false;
        let dragStartX = 0;
        let dragStartTime = 0;
        
        // 点击进度条跳转
        progressContainer.addEventListener('click', (e) => {
            if (!isDragging && audioElement.duration) {
                const rect = progressContainer.getBoundingClientRect();
                const percentage = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
                const targetTime = percentage * audioElement.duration;
                this.seekAudio(audioElement, targetTime);
            }
        });

        // 进度条拖拽开始
        progressHandle.addEventListener('mousedown', (e) => {
            isDragging = true;
            dragStartX = e.clientX;
            dragStartTime = audioElement.currentTime;
            document.body.style.userSelect = 'none'; // 防止拖拽时选中文本
            e.preventDefault();
        });

        // 进度条拖拽过程
        document.addEventListener('mousemove', (e) => {
            if (isDragging && audioElement.duration) {
                const rect = progressContainer.getBoundingClientRect();
                const deltaX = e.clientX - dragStartX;
                const deltaPercentage = deltaX / rect.width;
                const deltaTime = deltaPercentage * audioElement.duration;
                const newTime = Math.max(0, Math.min(audioElement.duration, dragStartTime + deltaTime));
                
                // 实时更新进度条显示（但不实际跳转音频）
                const percentage = (newTime / audioElement.duration) * 100;
                progressFill.style.width = percentage + '%';
                progressHandle.style.left = percentage + '%';
                currentTimeEl.textContent = this.formatTime(newTime);
            }
        });

        // 进度条拖拽结束
        document.addEventListener('mouseup', (e) => {
            if (isDragging && audioElement.duration) {
                const rect = progressContainer.getBoundingClientRect();
                const deltaX = e.clientX - dragStartX;
                const deltaPercentage = deltaX / rect.width;
                const deltaTime = deltaPercentage * audioElement.duration;
                const targetTime = Math.max(0, Math.min(audioElement.duration, dragStartTime + deltaTime));
                
                // 执行实际的音频跳转
                this.seekAudio(audioElement, targetTime);
            }
            isDragging = false;
            document.body.style.userSelect = ''; // 恢复文本选择
        });

        // 支持触摸设备的拖拽
        progressHandle.addEventListener('touchstart', (e) => {
            isDragging = true;
            dragStartX = e.touches[0].clientX;
            dragStartTime = audioElement.currentTime;
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (isDragging && audioElement.duration) {
                const rect = progressContainer.getBoundingClientRect();
                const deltaX = e.touches[0].clientX - dragStartX;
                const deltaPercentage = deltaX / rect.width;
                const deltaTime = deltaPercentage * audioElement.duration;
                const newTime = Math.max(0, Math.min(audioElement.duration, dragStartTime + deltaTime));
                
                const percentage = (newTime / audioElement.duration) * 100;
                progressFill.style.width = percentage + '%';
                progressHandle.style.left = percentage + '%';
                currentTimeEl.textContent = this.formatTime(newTime);
                e.preventDefault();
            }
        });

        document.addEventListener('touchend', (e) => {
            if (isDragging && audioElement.duration) {
                const rect = progressContainer.getBoundingClientRect();
                const touch = e.changedTouches[0];
                const deltaX = touch.clientX - dragStartX;
                const deltaPercentage = deltaX / rect.width;
                const deltaTime = deltaPercentage * audioElement.duration;
                const targetTime = Math.max(0, Math.min(audioElement.duration, dragStartTime + deltaTime));
                
                this.seekAudio(audioElement, targetTime);
            }
            isDragging = false;
        });

        // 时间更新
        audioElement.addEventListener('timeupdate', () => {
            if (!isDragging && audioElement.duration) {
                const percentage = (audioElement.currentTime / audioElement.duration) * 100;
                progressFill.style.width = percentage + '%';
                progressHandle.style.left = percentage + '%';
                
                currentTimeEl.textContent = this.formatTime(audioElement.currentTime);
                totalTimeEl.textContent = this.formatTime(audioElement.duration);

                // 保存播放进度
                this.saveBookProgress(this.currentBook.id, {
                    currentTime: audioElement.currentTime,
                    duration: audioElement.duration,
                    percentage: percentage
                });
            }
        });

        // 播放速度
        speedSelect.addEventListener('change', () => {
            audioElement.playbackRate = parseFloat(speedSelect.value);
        });

        // 音量控制
        volumeSlider.addEventListener('input', () => {
            audioElement.volume = parseFloat(volumeSlider.value);
        });

        // 书签功能
        addBookmarkBtnNew.addEventListener('click', () => {
            this.addBookmark();
        });

        // 注释掉强制跳转功能的事件监听器，因为该按钮已被隐藏
        // forceSeekBtnNew.addEventListener('click', () => {
        //     this.showForceSeekDialog(audioElement);
        // });

        console.log('音频播放器事件已绑定');
    }

    // 新增：安全的音频跳转方法
    seekAudio(audioElement, targetTime) {
        if (!audioElement || !audioElement.duration) {
            console.warn('音频未准备好，无法跳转');
            this.showMessage('音频未准备好，请稍后再试', 'warning');
            return;
        }

        // 确保目标时间在有效范围内
        const safeTargetTime = Math.max(0, Math.min(audioElement.duration, targetTime));
        
        console.log(`尝试跳转到: ${this.formatTime(safeTargetTime)} (${safeTargetTime.toFixed(2)}s)`);
        console.log(`音频总时长: ${this.formatTime(audioElement.duration)} (${audioElement.duration.toFixed(2)}s)`);
        
        // 检查缓冲状态
        if (audioElement.buffered.length > 0) {
            console.log('音频缓冲范围:');
            for (let i = 0; i < audioElement.buffered.length; i++) {
                const start = audioElement.buffered.start(i);
                const end = audioElement.buffered.end(i);
                console.log(`  缓冲段 ${i + 1}: ${this.formatTime(start)} - ${this.formatTime(end)} (${start.toFixed(2)}s - ${end.toFixed(2)}s)`);
            }
        }
        
        // 检查可跳转范围
        if (audioElement.seekable.length > 0) {
            console.log('音频可跳转范围:');
            for (let i = 0; i < audioElement.seekable.length; i++) {
                const start = audioElement.seekable.start(i);
                const end = audioElement.seekable.end(i);
                console.log(`  可跳转段 ${i + 1}: ${this.formatTime(start)} - ${this.formatTime(end)} (${start.toFixed(2)}s - ${end.toFixed(2)}s)`);
            }
        }
        
        // 改进的可跳转性检查
        let canSeek = false;
        let seekCheckMethod = 'none';
        
        // 方法1: 检查seekable范围
        if (audioElement.seekable.length > 0) {
            for (let i = 0; i < audioElement.seekable.length; i++) {
                const start = audioElement.seekable.start(i);
                const end = audioElement.seekable.end(i);
                if (safeTargetTime >= start && safeTargetTime <= end) {
                    canSeek = true;
                    seekCheckMethod = 'seekable';
                    break;
                }
            }
        }
        
        // 方法2: 如果seekable检查失败，检查buffered范围（更宽松的检查）
        if (!canSeek && audioElement.buffered.length > 0) {
            for (let i = 0; i < audioElement.buffered.length; i++) {
                const start = audioElement.buffered.start(i);
                const end = audioElement.buffered.end(i);
                // 给缓冲范围一些容错空间（提前0.5秒结束）
                if (safeTargetTime >= start && safeTargetTime <= (end - 0.5)) {
                    canSeek = true;
                    seekCheckMethod = 'buffered';
                    console.log(`使用缓冲范围检查，目标时间在缓冲范围内`);
                    break;
                }
            }
        }
        
        // 方法3: 对于音频开始部分，总是允许跳转（前30秒）
        if (!canSeek && safeTargetTime <= 30) {
            canSeek = true;
            seekCheckMethod = 'early-range';
            console.log(`目标时间在前30秒内，允许跳转`);
        }
        
        // 方法4: 如果音频已经播放过这个位置，允许跳转
        if (!canSeek && safeTargetTime <= audioElement.currentTime + 1) {
            canSeek = true;
            seekCheckMethod = 'played-range';
            console.log(`目标时间在已播放范围内，允许跳转`);
        }
        
        console.log(`跳转检查结果: ${canSeek ? '允许' : '拒绝'} (方法: ${seekCheckMethod})`);
        
        if (!canSeek) {
            console.warn('目标时间不在可跳转范围内:', safeTargetTime);
            // 给用户更具体的提示
            let message = '该位置暂时无法跳转';
            if (audioElement.buffered.length > 0) {
                const bufferedEnd = audioElement.buffered.end(audioElement.buffered.length - 1);
                const bufferedPercent = (bufferedEnd / audioElement.duration) * 100;
                message += `，当前已缓冲 ${bufferedPercent.toFixed(1)}%`;
            }
            message += '，请等待更多内容缓冲完成';
            this.showMessage(message, 'warning');
            return;
        }

        try {
            // 记录跳转前的状态
            const wasPlaying = !audioElement.paused;
            const oldTime = audioElement.currentTime;
            
            console.log(`执行跳转: ${this.formatTime(oldTime)} -> ${this.formatTime(safeTargetTime)}`);
            
            // 执行跳转
            audioElement.currentTime = safeTargetTime;
            
            // 如果之前在播放，跳转后继续播放
            if (wasPlaying) {
                audioElement.play().catch(error => {
                    console.error('跳转后播放失败:', error);
                });
            }
            
            console.log(`音频跳转成功: ${this.formatTime(safeTargetTime)}`);
            
        } catch (error) {
            console.error('音频跳转失败:', error);
            this.showMessage('跳转失败，请重试', 'error');
            
            // 尝试重新加载音频（作为备用方案）
            if (error.name === 'InvalidStateError') {
                console.log('尝试重新加载音频...');
                const currentSrc = audioElement.src;
                audioElement.load();
                audioElement.addEventListener('loadedmetadata', () => {
                    try {
                        audioElement.currentTime = safeTargetTime;
                        console.log('重新加载后跳转成功');
                    } catch (retryError) {
                        console.error('重新加载后跳转仍然失败:', retryError);
                    }
                }, { once: true });
            }
        }
    }

    // 新增：强制跳转方法（备用方案）
    forceSeekAudio(audioElement, targetTime) {
        console.log('尝试强制跳转...');
        
        if (!audioElement || !audioElement.duration) {
            console.warn('音频未准备好，无法强制跳转');
            return false;
        }

        const safeTargetTime = Math.max(0, Math.min(audioElement.duration, targetTime));
        
        try {
            // 暂停音频
            const wasPlaying = !audioElement.paused;
            audioElement.pause();
            
            // 直接设置时间，不进行任何检查
            audioElement.currentTime = safeTargetTime;
            
            // 等待一小段时间后恢复播放
            if (wasPlaying) {
                setTimeout(() => {
                    audioElement.play().catch(error => {
                        console.error('强制跳转后播放失败:', error);
                    });
                }, 100);
            }
            
            console.log(`强制跳转成功: ${this.formatTime(safeTargetTime)}`);
            return true;
            
        } catch (error) {
            console.error('强制跳转失败:', error);
            return false;
        }
    }

    // 播放进度管理
    getPlaybackProgress() {
        const progress = localStorage.getItem('audiobook_playback_progress');
        return progress ? JSON.parse(progress) : {};
    }

    getBookProgress(bookId) {
        const allProgress = this.getPlaybackProgress();
        return allProgress[bookId] || { currentTime: 0, duration: 0, percentage: 0 };
    }

    saveBookProgress(bookId, progress) {
        const allProgress = this.getPlaybackProgress();
        allProgress[bookId] = progress;
        localStorage.setItem('audiobook_playback_progress', JSON.stringify(allProgress));
    }

    // 书签功能
    loadBookmarks() {
        const bookmarks = this.getBookBookmarks(this.currentBook.id);
        this.renderBookmarks(bookmarks);
    }

    getBookBookmarks(bookId) {
        const allBookmarks = localStorage.getItem('audiobook_bookmarks');
        const bookmarks = allBookmarks ? JSON.parse(allBookmarks) : {};
        return bookmarks[bookId] || [];
    }

    addBookmark() {
        const audioElement = document.getElementById('audio-element');
        const currentTime = audioElement.currentTime;
        
        if (!audioElement.duration) {
            this.showMessage('请先加载音频', 'error');
            return;
        }

        const note = prompt('为这个书签添加备注：') || '';
        const bookmark = {
            id: Date.now(),
            time: currentTime,
            note: note,
            createdAt: new Date().toISOString()
        };

        const bookmarks = this.getBookBookmarks(this.currentBook.id);
        bookmarks.push(bookmark);
        bookmarks.sort((a, b) => a.time - b.time);

        this.saveBookBookmarks(this.currentBook.id, bookmarks);
        this.renderBookmarks(bookmarks);
        this.showMessage('书签添加成功', 'success');
    }

    saveBookBookmarks(bookId, bookmarks) {
        const allBookmarks = JSON.parse(localStorage.getItem('audiobook_bookmarks') || '{}');
        allBookmarks[bookId] = bookmarks;
        localStorage.setItem('audiobook_bookmarks', JSON.stringify(allBookmarks));
    }

    renderBookmarks(bookmarks) {
        const bookmarksList = document.getElementById('bookmarks-list');
        
        if (bookmarks.length === 0) {
            bookmarksList.innerHTML = '<p style="color: #666; text-align: center; padding: 1rem;">暂无书签</p>';
            return;
        }

        bookmarksList.innerHTML = bookmarks.map(bookmark => `
            <div class="bookmark-item">
                <div class="bookmark-info">
                    <div class="bookmark-time">${this.formatTime(bookmark.time)}</div>
                    ${bookmark.note ? `<div class="bookmark-note">${bookmark.note}</div>` : ''}
                </div>
                <div class="bookmark-actions">
                    <button class="btn btn-primary btn-small" onclick="app.jumpToBookmark(${bookmark.time})">
                        <i class="fas fa-play"></i> 跳转
                    </button>
                    <button class="btn btn-outline btn-small" onclick="app.deleteBookmark('${this.currentBook.id}', ${bookmark.id})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        `).join('');
    }

    jumpToBookmark(time) {
        const audioElement = document.getElementById('audio-element');
        audioElement.currentTime = time;
    }

    deleteBookmark(bookId, bookmarkId) {
        if (!confirm('确定要删除这个书签吗？')) return;

        const bookmarks = this.getBookBookmarks(bookId);
        const filteredBookmarks = bookmarks.filter(b => b.id !== bookmarkId);
        
        this.saveBookBookmarks(bookId, filteredBookmarks);
        this.renderBookmarks(filteredBookmarks);
        this.showMessage('书签已删除', 'success');
    }

    // 时间格式化
    formatTime(seconds) {
        if (!seconds || isNaN(seconds)) return '00:00';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // 任务数据格式化 - 将后端原始数据转换为前端期望的格式
    formatTaskData(task) {
        // 格式化任务类型显示
        const typeDisplayMap = {
            'txt': 'TXT文档',
            'epub': 'EPUB电子书',
            'pdf': 'PDF文档',
            'docx': 'Word文档',
            'url': '网页链接'
        };

        // 格式化文件大小
        const formatFileSize = (bytes) => {
            if (!bytes) return '';
            if (bytes < 1024) return `${bytes} B`;
            if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
            return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
        };

        // 格式化音频时长
        const formatDuration = (seconds) => {
            if (!seconds) return '';
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            if (hours > 0) {
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        };

        // 返回格式化后的任务数据
        return {
            ...task,
            // 添加前端期望的格式化字段
            typeDisplay: typeDisplayMap[task.task_type] || task.task_type || '未知类型',
            createdAtFormatted: this.formatDate(task.created_at, '未知时间'),
            updatedAtFormatted: this.formatDate(task.updated_at, '未知时间'),
            audioSizeFormatted: formatFileSize(task.file_size),
            durationFormatted: formatDuration(task.total_duration),
            filename: task.original_filename || task.title || '未知文件'
        };
    }

    // 日期格式化 - 安全地格式化各种日期字段
    formatDate(dateValue, fallback = '未知时间') {
        if (!dateValue) return fallback;

        try {
            const date = new Date(dateValue);
            if (isNaN(date.getTime())) {
                return fallback;
            }

            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: 'Asia/Shanghai'  // 明确指定东八区时间
            });
        } catch (error) {
            console.warn('日期格式化失败:', error, dateValue);
            return fallback;
        }
    }

    // 书籍选项
    showBookOptions(bookId) {
        // TODO: 实现书籍选项功能（重新命名、删除、分享等）
        alert('书籍选项功能即将推出');
    }

    // 手动刷新积分
    async handleRefreshPoints() {
        const refreshBtn = document.getElementById('btn-refresh-points');
        const icon = refreshBtn.querySelector('i');
        
        // 防止重复点击
        if (refreshBtn.disabled) return;
        
        refreshBtn.disabled = true;
        icon.classList.add('spinning');
        
        try {
            await this.syncUserDataFromServer();
            this.showMessage('积分已同步！', 'success');
        } catch (error) {
            console.error('同步积分失败:', error);
            this.showMessage('同步积分失败，请重试', 'error');
        } finally {
            refreshBtn.disabled = false;
            icon.classList.remove('spinning');
        }
    }

    // 增强版：手动状态同步（使用专门的同步API）
    async handleRefreshTasks() {
        const refreshBtn = document.getElementById('refresh-tasks-btn');
        const icon = refreshBtn.querySelector('i');

        // 防止重复点击
        if (refreshBtn.disabled) return;

        refreshBtn.disabled = true;
        refreshBtn.classList.add('refreshing');
        icon.classList.add('spinning');

        try {
            console.log('🔄 手动触发全面任务状态同步...');

            if (!this.user) {
                this.showMessage('请先登录', 'error');
                return;
            }

            // 显示开始同步的消息
            this.showMessage('🔄 正在检查任务状态...', 'info');

            // 使用专门的自动同步API进行全面检查
            const syncResponse = await fetch('/api/auto-sync-task-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: this.user.email,
                    autoMode: false // 手动模式，进行更全面的检查
                })
            });

            if (syncResponse.ok) {
                const syncResult = await syncResponse.json();
                console.log(`✅ 手动状态同步完成:`, syncResult.summary);

                if (syncResult.summary.updatedTasks > 0) {
                    // 重新加载任务列表以显示最新状态
                    await this.loadMyTasks();

                    // 显示详细的同步结果
                    const message = `🎉 成功修复了 ${syncResult.summary.updatedTasks} 个任务的状态！`;
                    this.showMessage(message, 'success');

                    // 显示每个修复的任务详情
                    if (syncResult.results && syncResult.results.length > 0) {
                        const fixedTasks = syncResult.results.filter(r => r.updated);
                        fixedTasks.forEach(result => {
                            if (result.newStatus === 'completed') {
                                console.log(`✅ 任务 ${result.taskId} 已修复为完成状态`);
                            }
                        });
                    }
                } else {
                    this.showMessage('✅ 所有任务状态都是最新的', 'success');
                }
            } else {
                const errorResult = await syncResponse.json();
                console.warn('专门同步API失败，回退到传统方法:', errorResult.error);

                // 回退到传统的同步方法
                this.showMessage('🔄 使用备用方法检查状态...', 'info');
                await this.syncTasksStatus();
            }

        } catch (error) {
            console.error('手动刷新任务状态失败:', error);
            this.showMessage('同步失败，请检查网络连接后重试', 'error');
        } finally {
            refreshBtn.disabled = false;
            refreshBtn.classList.remove('refreshing');
            icon.classList.remove('spinning');
        }
    }

    // 新首页按钮事件
    handleStartConversion() {
        if (this.user) {
            // 已登录，跳转到创作工坊
            this.switchPage('workshop');
        } else {
            // 未登录，显示登录模态框
            this.showLoginModal();
        }
    }

    // 演示按钮点击事件
    handleDemoClick() {
        // 滚动到示例音频区域
        const demoSection = document.querySelector('.demo-section');
        if (demoSection) {
            demoSection.scrollIntoView({ 
                behavior: 'smooth',
                block: 'center'
            });
            this.showMessage('请试听下方的音频示例，体验转换效果', 'info');
        }
    }

    showMessage(text, type = 'info') {
        const messageEl = document.getElementById('message');
        messageEl.textContent = text;
        messageEl.className = `message ${type}`;
        messageEl.classList.add('show');
        
        setTimeout(() => {
            messageEl.classList.remove('show');
        }, 3000);
    }

    // 重置音频库过滤按钮状态
    resetLibraryFilterButtons() {
        document.querySelectorAll('.library-filter-btn').forEach(btn => btn.classList.remove('active'));
        // 設置當前排序按鈕為活動狀態
        const activeBtn = document.querySelector(`[data-sort="${this.currentLibrarySort}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
    }

    // 新增：显示强制跳转对话框
    showForceSeekDialog(audioElement) {
        if (!audioElement || !audioElement.duration) {
            this.showMessage('音频未准备好', 'error');
            return;
        }

        const currentTimeStr = this.formatTime(audioElement.currentTime);
        const totalTimeStr = this.formatTime(audioElement.duration);
        
        const timeInput = prompt(
            `强制跳转到指定时间\n\n` +
            `当前时间: ${currentTimeStr}\n` +
            `总时长: ${totalTimeStr}\n\n` +
            `请输入目标时间（格式：分:秒 或 时:分:秒）：`,
            currentTimeStr
        );

        if (timeInput === null) {
            return; // 用户取消
        }

        const targetTime = this.parseTimeInput(timeInput.trim());
        
        if (targetTime === null) {
            this.showMessage('时间格式不正确，请使用 分:秒 或 时:分:秒 格式', 'error');
            return;
        }

        if (targetTime < 0 || targetTime > audioElement.duration) {
            this.showMessage(`时间超出范围，请输入 0 到 ${totalTimeStr} 之间的时间`, 'error');
            return;
        }

        // 尝试强制跳转
        const success = this.forceSeekAudio(audioElement, targetTime);
        if (success) {
            this.showMessage(`已强制跳转到 ${this.formatTime(targetTime)}`, 'success');
        } else {
            this.showMessage('强制跳转失败，请重试', 'error');
        }
    }

    // 新增：解析时间输入
    parseTimeInput(timeStr) {
        if (!timeStr) return null;

        // 移除所有空格
        timeStr = timeStr.replace(/\s/g, '');
        
        // 支持的格式：
        // MM:SS (分:秒)
        // HH:MM:SS (时:分:秒)
        // 纯数字（秒）
        
        // 纯数字（秒）
        if (/^\d+$/.test(timeStr)) {
            const seconds = parseInt(timeStr);
            return isNaN(seconds) ? null : seconds;
        }
        
        // MM:SS 格式
        const mmssMatch = timeStr.match(/^(\d{1,2}):(\d{1,2})$/);
        if (mmssMatch) {
            const minutes = parseInt(mmssMatch[1]);
            const seconds = parseInt(mmssMatch[2]);
            if (seconds >= 60) return null; // 秒数不能超过59
            return minutes * 60 + seconds;
        }
        
        // HH:MM:SS 格式
        const hhmmssMatch = timeStr.match(/^(\d{1,2}):(\d{1,2}):(\d{1,2})$/);
        if (hhmmssMatch) {
            const hours = parseInt(hhmmssMatch[1]);
            const minutes = parseInt(hhmmssMatch[2]);
            const seconds = parseInt(hhmmssMatch[3]);
            if (minutes >= 60 || seconds >= 60) return null; // 分钟和秒数不能超过59
            return hours * 3600 + minutes * 60 + seconds;
        }
        
        return null; // 无法解析
    }

    // 新增：增强版播放器相关事件
    toggleEnhancedOptions(checked) {
        // 在这里添加增强版播放器的逻辑
        console.log('增强版播放器选项:', checked);
    }

    // 新增：处理选项相关事件
    updateCostInfo(checked) {
        const costInfo = document.querySelector('.cost-info');
        if (costInfo) {
            costInfo.textContent = checked ? '消耗积分：20' : '消耗积分：10';
        }
    }

    // 清理资源
    destroy() {
        // 停止所有任务状态检查
        this.stopAllTaskStatusChecks();
        
        // 清理防抖定时器
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout);
            this.refreshTimeout = null;
        }
        
        // 清理增强版播放器
        if (this.enhancedPlayer) {
            this.enhancedPlayer.destroy();
            this.enhancedPlayer = null;
        }
        
        console.log('应用状态清理完成');
    }
}

// 初始化应用
const app = new AppState();

// 智能状态检查管理器 - 基于页面状态的优化轮询
class StatusCheckManager {
    constructor(appInstance) {
        this.app = appInstance;
        this.isPageVisible = !document.hidden;
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 页面可见性变化监听
        document.addEventListener('visibilitychange', () => {
            this.isPageVisible = !document.hidden;
            
            if (this.isPageVisible) {
                console.log('页面变为前台，检查是否需要恢复状态检查');
                // 页面变为可见时，重新启动状态检查
                setTimeout(() => {
                    if (this.shouldStartStatusCheck()) {
                        this.app.startProcessingTasksCheck();
                    }
                }, 1000);
            } else {
                console.log('页面变为后台，停止状态检查以节省资源');
                // 页面变为后台时，停止状态检查
                this.app.stopAllTaskStatusChecks();
            }
        });

        // 监听紧急停止信号
        window.addEventListener('storage', (event) => {
            if (event.key === 'EMERGENCY_STOP_POLLING' && event.newValue === 'true') {
                console.log('🚨 收到紧急停止轮询信号！');
                
                // 停止所有状态检查
                this.app.stopAllTaskStatusChecks();
                
                // 显示通知
                if (this.app && typeof this.app.showMessage === 'function') {
                    this.app.showMessage('🚨 紧急优化：已停止所有状态检查以减少服务器负载', 'warning');
                }
                
                // 清理现有任务数据缓存
                this.app.tasks = [];
                this.app.allTasks = [];
            }
        });

        // 定期检查是否需要启动状态检查（作为备用机制）
        setInterval(() => {
            // 检查紧急停止信号
            const emergencyStop = localStorage.getItem('EMERGENCY_STOP_POLLING');
            if (emergencyStop === 'true') {
                return;
            }

            // 只有在满足所有条件时才启动状态检查
            if (this.shouldStartStatusCheck() && !this.hasActiveStatusChecks()) {
                console.log('备用检查：启动状态检查');
                this.app.startProcessingTasksCheck();
            }
        }, 60000); // 每分钟检查一次作为备用
    }

    // 判断是否应该启动状态检查
    shouldStartStatusCheck() {
        return this.app.user && 
               this.isPageVisible &&
               (this.app.currentPage === 'workshop' || this.app.currentPage === 'my-tasks' || this.app.currentPage === 'home') &&
               this.hasProcessingTasks();
    }

    // 检查是否有正在处理的任务
    hasProcessingTasks() {
        const allTasks = [...(this.app.tasks || []), ...(this.app.allTasks || [])];
        return allTasks.some(task => task.status === 'pending');
    }

    // 检查是否有活动的状态检查器
    hasActiveStatusChecks() {
        return this.app.taskStatusCheckers && this.app.taskStatusCheckers.size > 0;
    }
}

// 初始化状态检查管理器
const statusCheckManager = new StatusCheckManager(app);